"""Scraper runner that pull dealsea frontpage deals and parse into models


"""

import logging

from common.db_utils import DbUtils
from common.env import ENV_PROD, ENV_DEV
from common.scraper_util import get_soup
from scraper.dealsea import soup_parser, db_ops
from scraper.dealsea import url_analyzer
from slugify import slugify

# get the fully-qualified logger (here: `root.__main__`)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def main():
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)

    url = "https://www.dealsea.com"
    dev_db_utils = DbUtils(env=ENV_DEV)
    prod_db_utils = DbUtils(env=ENV_PROD)

    logger.info(f"> Starting scraping {url}")
    soup = get_soup(url)

    logger.info(f"> Starting parsing soup")
    # step 1: scraping webpages for deals
    parsed_deal_posts = soup_parser.parse_deal_posts(soup)
    new_scraped_deals = [(i[0].id, i[0].post_id, i[0].source, i[0].store, i[0].override_store,
                          i[0].title, i[0].description, i[0].sales_price, i[0].original_price, i[0].status,
                          i[0].link_url, i[0].img_url, i[0].downloaded_img_path, i[0].created_at, i[0].updated_at,
                          slugify(i[0].title) + '_' + i[0].id)  # generated slug value
                         for i in parsed_deal_posts if i is not None]

    # flat_scraped_deal_items = [
    #     item[1]
    #     for deal_tuple in parsed_deal_posts
    #     for item in deal_tupleq
    # ]

    flat_scraped_deal_items = []
    for deal_tuple in parsed_deal_posts:
        for deal_item in deal_tuple[1]:
            flat_scraped_deal_items.append(deal_item)

    new_scraped_deal_items = [(i.item_id, i.get_deal_id(), i.post_id, i.source, i.item_title,
                               i.item_description, i.item_sales_price, i.item_original_price, i.item_url,
                               i.item_img_url,
                               i.item_downloaded_img_path, i.created_at, i.updated_at)
                              for i in flat_scraped_deal_items]

    # step 2: inserting these new deals scraped into scraped db as staging place
    db_ops.insert_scraped_deals(dev_db_utils, new_scraped_deals)
    db_ops.insert_scraped_deal_items(dev_db_utils, new_scraped_deal_items)

    # step 3: manually overwride some store names which are incorrectly recognized in scraped content
    db_ops.override_store_name_in_deals_tb(dev_db_utils)

    # step 4: analyzing url redirection and update dest_url in scraped tables
    url_analyzer.process_deal_urls(dev_db_utils)
    url_analyzer.process_deal_items_urls(dev_db_utils)

    # step 5: upload imgs to Cloudinary
    db_ops.update_image_urls_in_db(dev_db_utils)

    ##  step 6 v2: load news deals data into CG tables
    db_ops.insert_new_records_to_cg_db_v2(dev_db_utils, dev_db_utils)  # to local dev db
    db_ops.insert_new_records_to_cg_db_v2(dev_db_utils, prod_db_utils)  # to remote prod db

    # step 6 v1: inserting news deals into prod tables from staging (r)
    # TODO remove once v2 is fully productionized
    # db_ops.insert_new_records_to_cg_db(dev_db_utils, prod_db_utils)

    # step 7: close conn
    dev_db_utils.close()
    prod_db_utils.close()


if __name__ == "__main__":
    main()
