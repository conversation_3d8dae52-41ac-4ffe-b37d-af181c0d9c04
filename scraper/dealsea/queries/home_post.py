'''
Queries for deals
'''
from scraper.dealsea.soup_parser import SCRAPED_IMAGES_PATH

select_new_scraped_deals_query = f"""
        SELECT 
            sd.id                                       AS id,
            sd.post_id                                  AS scraped_id,
            sd.source                                   AS scraped_source,
            sd.store                                    AS display_store_name,
            hs.id                                       AS override_store_id,
            sd.title                                    AS title,
            sd.description                              AS description,
            sd.sales_price                              AS sales_price,
            sd.original_price                           AS original_price,
            sd.status                                   AS status,
            link_final_url                              AS dest_url,
            null                                        AS extra_paragraph,
            sd.created_at                               AS start_datetime,
            (sd.created_at  + INTERVAL '5 day')         AS end_datetime,
            sd.created_at                               AS created_at,
            sd.updated_at                               AS updated_at,
            sd.slug                                     AS slug
        FROM scraped.deals sd
              inner join public.home_store hs
                         on lower(coalesce(sd.override_store, sd.store)) = lower(hs.name)
        where link_final_url is not null
    """

insert_new_home_posts_query = f"""
        INSERT INTO public.home_post(id,
                                     scraped_id,
                                     scraped_source,
                                     display_store_name,
                                     override_store_id,
                                     title,
                                     description,
                                     sales_price,
                                     original_price,
                                     status,
                                     dest_url,
                                     extra_paragraph,
                                     start_datetime,
                                     end_datetime,
                                     created_at,
                                     updated_at,
                                     slug)
        values (%s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s)
        ON CONFLICT(id) DO NOTHING;
             """

select_new_scraped_deal_images_query = f"""
        select 
            hd.id                                                                 as post_id,
            sdi.id                                                                as scraped_item_id,
            sdi.source                                                            as scraped_source,
            sdi.media_img_url                                                     as img_url,
            replace(sdi.item_downloaded_img_path, '{SCRAPED_IMAGES_PATH}/', '')   as img_file,
            sdi.created_at,
            sdi.updated_at
        from public.home_post hd
        inner join scraped.deal_items sdi on hd.id = sdi.deal_id
        where sdi.item_downloaded_img_path is not null
        
        union all
        
        select 
            hd.id                                                           as post_id,
            ''                                                              as scraped_item_id,
            sd.source                                                       as scraped_source,
            sd.media_img_url                                                as img_url,
            replace(sd.downloaded_img_path, '{SCRAPED_IMAGES_PATH}/', '')   as img_file,
            sd.created_at,
            sd.updated_at
        FROM public.home_post hd
        inner join scraped.deals sd on hd.id = sd.id
        where sd.downloaded_img_path is not null
    """

insert_new_home_postimages_query = f"""
    INSERT INTO public.home_postimage(post_id,
                                      scraped_item_id,
                                      scraped_source,
                                      img_url,
                                      img_file,
                                      created_at,
                                      updated_at)
    values (%s, COALESCE(%s, ''), %s, %s, %s, %s, %s)
    ON CONFLICT(post_id, scraped_item_id) DO NOTHING;
    """

select_new_scraped_deal_items_query = f"""
        select sdi.id             as id,
                sdi.post_id        as scraped_id,
                sdi.source         as scraped_source,
                sdi.title          as title,
                sdi.description    as description,
                sdi.sales_price    as sales_price,
                sdi.original_price as original_price,
                sdi.item_final_url as dest_url,
                sdi.created_at     as created_at,
                sdi.updated_at     as updated_at,
                sdi.deal_id        as deal_id
         from scraped.deal_items sdi
         inner join public.home_post hd on sdi.deal_id = hd.id;
    """

insert_new_home_postitems_query = f"""
        INSERT INTO public.home_postitem(id,
                                             scraped_id,
                                             scraped_source,
                                             title,
                                             description,
                                             sales_price,
                                             original_price,
                                             dest_url,
                                             created_at,
                                             updated_at,
                                             post_id)
        values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT(id) DO NOTHING;
    """

select_new_scraped_deals_query_v2 = f"""
        SELECT 
            id,
            post_id,
            source,
            store,
            override_store,
            title,
            description,
            sales_price,
            original_price,
            status,
            link_url,
            link_final_url, 
            img_url,
            downloaded_img_path,
            created_at,
            updated_at,
            slug,
            media_img_url
        FROM scraped.deals
        WHERE created_at >= current_timestamp - interval '5 days'
            AND link_final_url is not null
    """

# keep its schema in sync with scraped.deals
create_temp_new_scraped_deals_query = f"""
    DROP TABLE IF EXISTS temp_new_scraped_deals;
    CREATE TABLE temp_new_scraped_deals
    (
    id                  text primary key,     -- unique identifier of this deal, formular: md5(source__post_id)
    post_id             text        NOT NULL, -- identifier of a deal post at its source, e.g.1930857
    source              text        NOT NULL, -- where the deal post is scraped from, e.g. 'dealsea'
    store               text        NOT NULL,
    override_store      text,
    title               text        NOT NULL,
    description         text        NOT NULL,
    sales_price         Numeric(10, 2),
    original_price      Numeric(10, 2),
    status              text,
    link_url            text        NOT NULL,
    link_final_url      text,                 -- from http redirection, added later
    img_url             text        NOT NULL,
    downloaded_img_path text        NOT NULL,
    created_at          timestamptz NOT NULL,
    updated_at          timestamptz NOT NULL,
    slug varchar(200) not null unique,
	media_img_url text,
    UNIQUE (post_id, source)
    )
    """

insert_temp_new_scraped_deals_query = f"""
    INSERT INTO temp_new_scraped_deals(
        id, 
        post_id,
        source,
        store,
        override_store, 
        title,
        description, 
        sales_price, 
        original_price,
        status,
        link_url, 
        link_final_url, 
        img_url, 
        downloaded_img_path,
        created_at,
        updated_at,
        slug,
        media_img_url
         )
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
    """

insert_new_home_posts_query_v2 = f"""
        INSERT INTO public.home_post(id,
                             scraped_id,
                             scraped_source,
                             display_store_name,
                             override_store_id,
                             title,
                             description,
                             sales_price,
                             original_price,
                             status,
                             dest_url,
                             extra_paragraph,
                             start_datetime,
                             end_datetime,
                             created_at,
                             updated_at,
                             slug)
        SELECT
        sd.id                                       AS id,
        sd.post_id                                  AS scraped_id,
        sd.source                                   AS scraped_source,
        sd.store                                    AS display_store_name,
        hs.id                                       AS override_store_id,
        sd.title                                    AS title,
        sd.description                              AS description,
        sd.sales_price                              AS sales_price,
        sd.original_price                           AS original_price,
        sd.status                                   AS status,
        sd.link_final_url                           AS dest_url,
        null                                        AS extra_paragraph,
        sd.created_at                               AS start_datetime,
        (sd.created_at  + INTERVAL '5 day')         AS end_datetime,
        sd.created_at                               AS created_at,
        sd.updated_at                               AS updated_at,
        sd.slug                                     AS slug
        FROM temp_new_scraped_deals sd
        INNER JOIN public.home_store hs
            ON LOWER(COALESCE(sd.override_store, sd.store)) = LOWER(hs.name)
        WHERE link_final_url IS NOT NULL
        ON CONFLICT(id) DO NOTHING
             """

select_new_scraped_deal_images_query_v2 = f"""
        SELECT 
            sdi.deal_id                                                           as deal_id,
            sdi.id                                                                as scraped_item_id,
            sdi.source                                                            as scraped_source,
            sdi.media_img_url                                                     as img_url,
            replace(sdi.item_downloaded_img_path, '{SCRAPED_IMAGES_PATH}/', '')   as img_file,
            sdi.created_at,
            sdi.updated_at
        FROM scraped.deal_items sdi
        WHERE sdi.item_downloaded_img_path is not null
        
        UNION ALL 
        
        SELECT 
            sd.id                                                           as deal_id,
            ''                                                              as scraped_item_id,
            sd.source                                                       as scraped_source,
            sd.media_img_url                                                as img_url,
            replace(sd.downloaded_img_path, '{SCRAPED_IMAGES_PATH}/', '')   as img_file,
            sd.created_at,
            sd.updated_at
        FROM scraped.deals sd
        WHERE sd.downloaded_img_path is not null
    """

create_temp_new_scraped_deal_images_query = """
                                            DROP TABLE IF EXISTS temp_new_scraped_deal_images;
                                            CREATE TABLE temp_new_scraped_deal_images
                                            (
                                                deal_id         text        NOT NULL,
                                                scraped_item_id text        NOT NULL,
                                                scraped_source  text        NOT NULL,
                                                img_url         text,
                                                img_file        text        NOT NULL,
                                                created_at      timestamptz NOT NULL,
                                                updated_at      timestamptz NOT NULL
                                            ) \
                                            """

insert_temp_new_scraped_deal_images_query = f"""
    INSERT INTO temp_new_scraped_deal_images(
        deal_id,
        scraped_item_id,
        scraped_source,
        img_url,
        img_file,
        created_at,
        updated_at
         )
    VALUES (%s, %s, %s, %s, %s, %s, %s) ;
"""

insert_new_home_postimages_query_v2 = f"""
    INSERT INTO public.home_postimage (post_id,
                                      scraped_item_id,
                                      scraped_source,
                                      img_url,
                                      img_file,
                                      created_at,
                                      updated_at)
    SELECT 
        hd.id as post_id,
        tn.scraped_item_id,
        tn.scraped_source,
        tn.img_url,
        tn.img_file,
        tn.created_at,
        tn.updated_at
    FROM temp_new_scraped_deal_images tn
    INNER JOIN public.home_post hd  on hd.id = tn.deal_id
    ON CONFLICT(post_id, scraped_item_id) DO NOTHING;
    """

select_new_scraped_deal_items_query_v2 = f"""
        select  id ,
                deal_id,
                post_id,
                source,
                title,
                description,
                sales_price,
                original_price,
                item_url,
                item_final_url,
                item_img_url,
                item_downloaded_img_path,
                created_at,
                updated_at
         from scraped.deal_items sdi
         WHERE created_at >= current_timestamp - interval '5 days'
    """

# keep its schema in sync with scraped.deal_items
create_temp_new_scraped_deal_items_query = """
                                           DROP TABLE IF EXISTS temp_new_scraped_deal_items;
                                           CREATE TEMP TABLE temp_new_scraped_deal_items
                                           (
                                               id                       text primary key,     -- unique identifier of this deal item, formular: md5(source__post_id__item_id)
                                               deal_id                  text        NOT NULL, -- foreign key to scraped.deals.id
                                               post_id                  text        NOT NULL, -- foreign key to scraped.deals.post_id
                                               source                   text        NOT NULL, -- foreign key to scraped.deals.source
                                               title                    text        NOT NULL,
                                               description              text,
                                               sales_price              Numeric(10, 2),
                                               original_price           Numeric(10, 2),
                                               item_url                 text,
                                               item_final_url           text,                 -- from http redirection, added later
                                               item_img_url             text,
                                               item_downloaded_img_path text,
                                               created_at               timestamptz NOT NULL,
                                               updated_at               timestamptz NOT NULL
                                           )
                                           """

insert_temp_new_scraped_deal_items_query = f"""
    INSERT INTO temp_new_scraped_deal_items(
        id,
        deal_id,
        post_id,
        source,
        title,
        description,
        sales_price,
        original_price,
        item_url,
        item_final_url,
        item_img_url,
        item_downloaded_img_path,
        created_at,
        updated_at
         )
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) ;
    """

insert_new_home_postitems_query_v2 = f"""
    INSERT INTO public.home_postitem (id,
                                             scraped_id,
                                             scraped_source,
                                             title,
                                             description,
                                             sales_price,
                                             original_price,
                                             dest_url,
                                             created_at,
                                             updated_at,
                                             post_id)
         SELECT 
                sdi.id             as id,
                sdi.post_id        as scraped_id,
                sdi.source         as scraped_source,
                sdi.title          as title,
                sdi.description    as description,
                sdi.sales_price    as sales_price,
                sdi.original_price as original_price,
                sdi.item_final_url as dest_url,
                sdi.created_at     as created_at,
                sdi.updated_at     as updated_at,
                sdi.deal_id        as deal_id
    FROM temp_new_scraped_deal_items sdi
    INNER JOIN public.home_post hd on sdi.deal_id = hd.id
        ON CONFLICT(id) DO NOTHING;

"""
