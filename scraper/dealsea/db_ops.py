import logging
import os
import concurrent.futures

from common.imagekit_utils import ImagekitUtils
from scraper.dealsea.queries.home_post import *
from scraper.dealsea.queries.scraped_deals import update_deals_store_name_query, insert_new_scraped_dealitems_query, \
    insert_new_scraped_deals_query

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def override_store_name_in_deals_tb(dev_db_utils):
    """ translate urls to their post-direction urls """
    logger.debug(f'override store_name in scraped.deals table using scraped.store_name_override..')
    dev_db_utils.run_update_query(update_deals_store_name_query)
    dev_db_utils.commit()


# def update_db_with_redirected_urls(conn, post_redirection_results, update_query):
#     """ translate urls to their post-direction urls """
#     logger.debug(f'getting redirected urls...')
#     ts = datetime.now(timezone.utc)
#     try:
#         with conn:
#             with  conn.cursor() as cursor:
#                 for item in post_redirection_results:
#                     cursor.execute(update_query, (item[2],
#                                                   ts,
#                                                   item[0],
#                                                   item[1])
#                                    )
#
#                 # commit the changes to the database
#                 conn.commit()
#
#     except (Exception, psycopg2.DatabaseError) as error:
#         logger.error(error, stack_info=True, exc_info=True)
#     finally:
#         cursor.close()


# def get_scraped_urls(conn, select_query):
#     """ translate urls to their post-direction urls """
#     logger.debug(f'getting redirected urls...')
#
#     try:
#         with conn:
#             with  conn.cursor() as cursor:
#                 cursor.execute(select_query)
#                 print(select_query)
#                 rows = cursor.fetchall()
#
#                 return rows
#
#     except (Exception, psycopg2.DatabaseError) as error:
#         logger.error(error, stack_info=True, exc_info=True)
#     finally:
#         cursor.close()


def insert_scraped_deals(dev_db_utils, records):
    """ Insert a new scraped_deal into the cashback_goat.scraped_deals table """

    logger.debug(f'Inserting {len(records)} new scraped deals into cashback_goat.scraped.deals table..')
    dev_db_utils.run_batch_update_query(insert_new_scraped_deals_query, records)
    dev_db_utils.commit()


def insert_scraped_deal_items(dev_db_utils, records):
    """ Insert a new scraped_deal into the cashback_goat.scraped_deals table """
    logger.debug(f'Inserting {len(records)} new scraped dealitems into cashback_goat.scraped.deal_items table..')
    dev_db_utils.run_batch_update_query(insert_new_scraped_dealitems_query, records)
    dev_db_utils.commit()

# TODO: deprecate by insert_new_records_to_cg_db_v2()
def insert_new_records_to_cg_db(dev_db_utils, prod_db_utils):
    """ Insert new deal records to cashback_goat.public.deal_* tables from scraped_* tables """

    new_deals_to_add = dev_db_utils.run_select_query(select_new_scraped_deals_query)
    if len(new_deals_to_add) > 0:
        logger.info(f'Inserting new deal records to cashback_goat.public.home_deal tb..')
        # dev_db_utils.run_batch_insert_query(insert_query_template=insert_new_home_posts_query,
        #                                     argslist=new_deals_to_add)
        prod_db_utils.run_batch_insert_query(insert_query_template=insert_new_home_posts_query,
                                             argslist=new_deals_to_add)

    new_dealimages_to_add = dev_db_utils.run_select_query(select_new_scraped_deal_images_query)
    if len(new_dealimages_to_add) > 0:
        logger.info(f'Inserting new deal records to cashback_goat.public.home_dealimage tb..')
        # dev_db_utils.run_batch_insert_query(insert_query_template=insert_new_home_postimages_query,
        #                                     argslist=new_dealimages_to_add)
        prod_db_utils.run_batch_insert_query(insert_query_template=insert_new_home_postimages_query,
                                             argslist=new_dealimages_to_add)

    new_dealitems_to_add = dev_db_utils.run_select_query(select_new_scraped_deal_items_query)
    if len(new_dealitems_to_add) > 0:
        logger.info(f'Inserting new deal records to cashback_goat.public.home_dealitem tb..')
        # dev_db_utils.run_batch_insert_query(insert_query_template=insert_new_home_postitems_query,
        #                                     argslist=new_dealitems_to_add)
        prod_db_utils.run_batch_insert_query(insert_query_template=insert_new_home_postitems_query,
                                             argslist=new_dealitems_to_add)


def insert_new_records_to_cg_db_v2(source_db_utils, target_db_utils):
    """ Insert new deal records to cashback_goat.public.deal_* tables from scraped_* tables """

    # part 1 : sync to public.home_post
    new_deals_to_add = source_db_utils.run_select_query(select_new_scraped_deals_query_v2)
    if len(new_deals_to_add) > 0:
        logger.info(f'Inserting {len(new_deals_to_add)} new deal records to cashback_goat.public.home_post tb..')
        # Create and insert into a temp table for new_deals at destination db  (most recent 5 days)
        target_db_utils.execute_query(create_temp_new_scraped_deals_query)
        target_db_utils.execute_query_in_batch(query=insert_temp_new_scraped_deals_query,
                                               argslist=new_deals_to_add)
        # update home_post using temp table
        target_db_utils.execute_query(insert_new_home_posts_query_v2)
        # Clean up
        target_db_utils.execute_query("DROP TABLE IF EXISTS temp_new_scraped_deals")


    # part 2 : sync to public.home_dealimage
    new_dealimages_to_add = source_db_utils.run_select_query(select_new_scraped_deal_images_query_v2)
    if len(new_dealimages_to_add) > 0:
        logger.info(f'Inserting {len(new_dealimages_to_add)} new dealimages to cashback_goat.public.home_dealimage tb..')
        # Create and insert into a temp table for new_deals (most recent 5 days)
        target_db_utils.execute_query(create_temp_new_scraped_deal_images_query)
        target_db_utils.execute_query_in_batch(query=insert_temp_new_scraped_deal_images_query,
                                               argslist=new_dealimages_to_add)
        # update home_post using temp table
        target_db_utils.execute_query(insert_new_home_postimages_query_v2)
        # Clean up
        target_db_utils.execute_query("DROP TABLE IF EXISTS temp_new_scraped_deal_images")

    # part 2 : sync to public.home_dealitem
    new_dealitems_to_add = source_db_utils.run_select_query(select_new_scraped_deal_items_query_v2)
    if len(new_dealitems_to_add) > 0:
        logger.info(f'Inserting {len(new_dealitems_to_add)} new deal items to cashback_goat.public.home_dealitem tb..')
        # Create and insert into a temp table for new_dealitems (most recent 5 days)
        target_db_utils.execute_query(create_temp_new_scraped_deal_items_query)
        target_db_utils.execute_query_in_batch(query=insert_temp_new_scraped_deal_items_query,
                                               argslist=new_dealitems_to_add)
        # update home_post using temp table
        target_db_utils.execute_query(insert_new_home_postitems_query_v2)
        # Clean up
        target_db_utils.execute_query("DROP TABLE IF EXISTS temp_new_scraped_deal_items")


def update_image_urls_in_db(dev_db_utils, max_workers=10):
    """Update the database with ImageKit URLs for images using multi-threading."""
    logger.info("> Uploading images to ImageKit with multi-threading")

    # Query deals with images that need to be uploaded to ImageKit
    deals_to_upload_img = dev_db_utils.run_select_query(
        """
        SELECT id,
               post_id,
               downloaded_img_path
        FROM scraped.deals
        WHERE created_at > NOW() - INTERVAL '3 DAY'
          AND downloaded_img_path IS NOT NULL
          AND media_img_url IS NULL
        """)

    imagekit = ImagekitUtils()
    
    # Function to process a single deal image
    def process_deal_image(deal):
        deal_id, post_id, downloaded_img_path = deal
        
        if downloaded_img_path and os.path.exists(downloaded_img_path):
            try:
                file_name = downloaded_img_path.rsplit("/", 1)[-1]
                dest_folder = downloaded_img_path.replace('/home/<USER>/Pictures/scraped/deal_posts/', '').rsplit("/", 1)[0]

                # Upload file to ImageKit
                img_url = imagekit.upload_image(src_img_file=downloaded_img_path,
                                                dest_file_name=file_name,
                                                dest_folder=f'cashback_goat/media/posts/{dest_folder}',
                                                dest_tags=['post', 'dealsea'])

                if img_url:
                    return (img_url, deal_id)
            except Exception as e:
                logger.error(f"Unexpected error uploading deal image {downloaded_img_path}: {e}")
        
        return None

    # Use ThreadPoolExecutor to process images in parallel
    deal_updates = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(process_deal_image, deal) for deal in deals_to_upload_img]
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            if result:
                deal_updates.append(result)

    # Batch update deals table
    if deal_updates:
        dev_db_utils.run_batch_update_query(
            "UPDATE scraped.deals SET media_img_url = %s WHERE id = %s",
            deal_updates
        )
        logger.info(f"Updated {len(deal_updates)} deal images with ImageKit URLs")

    # Query deal items with images that need to be uploaded to ImageKit
    deal_items_to_upload_img = dev_db_utils.run_select_query(
        """
        SELECT id,
               deal_id,
               post_id,
               item_downloaded_img_path
        FROM scraped.deal_items
        WHERE created_at > NOW() - INTERVAL '3 DAY'
          AND item_downloaded_img_path IS NOT NULL
          AND media_img_url IS NULL
        """)
    
    # Function to process a single deal item image
    def process_item_image(item):
        item_id, deal_id, post_id, downloaded_img_path = item
        
        if downloaded_img_path and os.path.exists(downloaded_img_path):
            try:
                file_name = downloaded_img_path.rsplit("/", 1)[-1]
                dest_folder = downloaded_img_path.replace('/home/<USER>/Pictures/scraped/deal_posts/', '').rsplit("/", 1)[0]

                # Upload file to ImageKit
                img_url = imagekit.upload_image(src_img_file=downloaded_img_path,
                                                dest_file_name=file_name,
                                                dest_folder=f'cashback_goat/media/posts/{dest_folder}',
                                                dest_tags=['post', 'dealsea', 'post-items'])
                if img_url:
                    return (img_url, item_id)
            except Exception as e:
                logger.error(f"Unexpected error uploading deal item image: {e}")
        
        return None

    # Process item images in parallel
    item_updates = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(process_item_image, item) for item in deal_items_to_upload_img]
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            if result:
                item_updates.append(result)

    # Batch update deal items table
    if item_updates:
        dev_db_utils.run_batch_update_query(
            "UPDATE scraped.deal_items SET media_img_url = %s WHERE id = %s",
            item_updates
        )
        logger.info(f"Updated {len(item_updates)} deal item images with ImageKit URLs")
