import csv
import os
from collections import namedtuple

from dotenv import load_dotenv

from common.db_utils import DbUtils
from common.env import ENV_DEV

# header for ../data/aff__cj_store_image.csv
CSV_HEADER = ['advertiser_id', 'advertiser_name', 'image_url', 'network']
Mapping = namedtuple('Mapping', CSV_HEADER)  # Define a named tuple for better readability


class AdvertiserImage:
    # csv file of advertiser_name (of all networks) to store_name mappings
    def __init__(self, csv_path='../data/aff__cj_store_image.csv', delimiter='|'):
        self.csv_path = csv_path
        self.delimiter = delimiter
        self.ensure_file_exists()
        self.mappings = self.load_mappings()
        load_dotenv()
        self.dev_db_utils = DbUtils(env=ENV_DEV)

    def ensure_file_exists(self):
        """Create the CSV file if it doesn't exist"""
        print(f"Ensuring csv file exists at: {self.csv_path}")
        if not os.path.exists(os.path.dirname(self.csv_path)):
            os.makedirs(os.path.dirname(self.csv_path))

        if not os.path.exists(self.csv_path):
            with open(self.csv_path, 'w', newline='') as file:
                writer = csv.writer(file, delimiter=self.delimiter)
                writer.writerow(CSV_HEADER)

    def load_mappings(self):
        """Load the mappings from CSV into memory"""
        try:
            mappings = []

            with open(self.csv_path, 'r', newline='') as file:
                reader = csv.DictReader(file, delimiter=self.delimiter)
                for row in reader:
                    if not row['advertiser_id'] or not row['advertiser_name'] or not row['image_url'] or not row[
                        'network']:
                        print(f"Skipping row: {row}")
                        continue

                    advertiser_id = row['advertiser_id']
                    advertiser_name = row['advertiser_name']
                    image_url = row['image_url']
                    network = row['network']

                    # Add the mapping
                    mappings.append(Mapping(advertiser_id, advertiser_name, image_url, network))

            print(f"Loaded {len(mappings)} valid mappings from total of {reader.line_num - 1} CSV rows")
            return mappings
        except Exception as e:
            print(f"Error loading advertiser mapping: {e}")
            raise e

    def create_affiliate_store_mapping_tb(self):
        """Create and update table scraped.aff__store_mapping for advertiser_store_mapping"""
        self.dev_db_utils.execute_query("""
                                        CREATE TABLE IF NOT EXISTS scraped.aff__advertiser_image (
                                            advertiser_id   VARCHAR(255),
                                            advertiser_name VARCHAR(255),
                                            image_url       TEXT,
                                            network         VARCHAR(20),
                                            UNIQUE (advertiser_id, network)
                                        )
                                        """)

        # Insert the mappings into the temporary table
        for map_item in self.mappings:
            self.dev_db_utils.execute_query("""
                                            INSERT INTO scraped.aff__advertiser_image (advertiser_id, advertiser_name, image_url, network)
                                            VALUES (%s, %s, %s, %s)
                                            ON CONFLICT(advertiser_id, network) DO UPDATE
                                                SET advertiser_name = EXCLUDED.advertiser_name,
                                                    image_url       = EXCLUDED.image_url
                                            """, (map_item.advertiser_id, map_item.advertiser_name,
                                                  map_item.image_url, map_item.network))
        print(f">>inserted {len(self.mappings)} records into scraped.aff__advertiser_image.")

    def get_advertiser_images(self, advertiser_name):
        """Get mapping as a dictionary: {advertiser_name: store_name}"""
        advertiser_images = []
        for mapping in self.mappings:
            if mapping.advertiser_name == advertiser_name:
                advertiser_images.append(mapping.image_url)
        return advertiser_images

    def get_store_to_network_mapping(self):
        """Get mapping as a dictionary: {store_name: network_name}"""
        store_to_network = {}
        for mapping in self.mappings:
            store_to_network[mapping.store_name] = mapping.network
        return store_to_network


def main():
    # Example usage
    mapping = AdvertiserImage()
    mapping.create_affiliate_store_mapping_tb()

    # Get all entries
    advertiser_images = mapping.get_advertiser_images('Chirp')
    print(f">> Got {len(advertiser_images)} images for Chirp..")

    for map_item in mapping.mappings:
        print(
            f"advertiser={map_item.advertiser_id} | {map_item.advertiser_name} -> store={map_item.image_url} -> network={map_item.network}")


if __name__ == "__main__":
    main()
