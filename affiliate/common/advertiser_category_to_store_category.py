import csv
import os
from collections import namedtuple

from dotenv import load_dotenv

from common.db_utils import DbUtils
from common.env import ENV_DEV

# header for ../data/aff__store_mapping.csv
CSV_HEADER = ['storecategory_name', 'advertiser_category_name', 'network']
Mapping = namedtuple('Mapping', CSV_HEADER)  # Define a named tuple for better readability


class AdvertiserCategoryToStoreCategory:
    # csv file of advertiser_category_name (of all networks) to storecategory_name mappings
    def __init__(self, csv_path='../data/aff__storecategory_mapping.csv', delimiter='|'):
        self.csv_path = csv_path
        self.delimiter = delimiter
        self.ensure_file_exists()

        self.mappings = self.load_mappings()
        self.advertiser_category_to_store_category_mapping = self.get_advertiser_category_to_store_category_mapping()  # Structure: {advertiser_category_name: storecategory_name}
        load_dotenv()
        self.dev_db_utils = DbUtils(env=ENV_DEV)

    def ensure_file_exists(self):
        """Create the CSV file if it doesn't exist"""
        print(f"Ensuring csv file exists at: {self.csv_path}")
        if not os.path.exists(os.path.dirname(self.csv_path)):
            os.makedirs(os.path.dirname(self.csv_path))

        if not os.path.exists(self.csv_path):
            with open(self.csv_path, 'w', newline='') as file:
                writer = csv.writer(file, delimiter=self.delimiter)
                writer.writerow(CSV_HEADER)

    def load_mappings(self):
        """Load the mappings from CSV into memory"""
        try:
            mappings = []

            with open(self.csv_path, 'r', newline='') as file:
                reader = csv.DictReader(file, delimiter=self.delimiter)
                for row in reader:
                    if not row['storecategory_name'] or not row['advertiser_category_name'] or not row['network']:
                        print(f"Skipping row due to missing value: {row}")
                        continue

                    advertiser_category_name = row['advertiser_category_name']
                    storecategory_name = row['storecategory_name']
                    network = row.get('network', 'unknown')  # Default to 'unknown' for backward compatibility

                    # Add the mapping
                    mappings.append(Mapping(storecategory_name, advertiser_category_name, network))

            print(f"Loaded {len(mappings)} valid mappings from total of {reader.line_num - 1} CSV rows")
            return mappings
        except Exception as e:
            print(f"Error loading advertiser mapping: {e}")
            raise e

    def get_storecategory_name_for_advertiser(self, advertiser_category_name):
        """Get the store name for an active advertiser name"""
        return self.advertiser_category_to_store_category_mapping.get(advertiser_category_name)

    def get_network_for_store(self, storecategory_name):
        """Get the network for a store name"""
        return self.store_category_to_network_mapping.get(storecategory_name)

    def get_networks_for_advertiser(self, advertiser_category_name):
        """Get all networks available for an advertiser"""
        if advertiser_category_name in self.advertiser_category_to_store_category_mapping:
            return list(self.advertiser_category_to_store_category_mapping[advertiser_category_name].keys())
        return []

    def get_advertiser_category_to_store_category_mapping(self):
        """Get mapping as a dictionary: {advertiser_category_name: storecategory_name}"""
        advertiser_to_store = {}
        for record in self.mappings:
            advertiser_to_store[record.advertiser_category_name] = record.storecategory_name
        return advertiser_to_store



    def create_affiliate_store_category_mapping_tb(self):
        """Create and update table scraped.aff__storecategory_mapping for category mapping"""
        self.dev_db_utils.execute_query("""
                                        CREATE TABLE IF NOT EXISTS scraped.aff__storecategory_mapping (
                                            storecategory_name      VARCHAR(255),
                                            advertiser_category_name VARCHAR(255),
                                            network         VARCHAR(20),
                                            UNIQUE (storecategory_name, advertiser_category_name, network)
                                        )
                                        """)

        # Insert the mappings into the temporary table
        for map_item in self.mappings:
            self.dev_db_utils.execute_query("""
                                            INSERT INTO scraped.aff__storecategory_mapping (storecategory_name, advertiser_category_name, network)
                                            VALUES (%s, %s, %s)
                                            ON CONFLICT(storecategory_name, advertiser_category_name, network) DO NOTHING
                                            """, (map_item.storecategory_name, map_item.advertiser_category_name,
                                                  map_item.network))
        print(f">>inserted {len(self.mappings)} records into scraped.aff__storecategory_mapping.")


def main():
    # Example usage
    mappingObj = AdvertiserCategoryToStoreCategory(csv_path='../data/aff__storecategory_mapping.csv')

    # Get all entries
    advertiser_category_to_store_category_mapping = mappingObj.get_advertiser_category_to_store_category_mapping()
    print(f">> Got {len(advertiser_category_to_store_category_mapping)} advertiser categories mapped to store categories..")
    mappingObj.create_affiliate_store_category_mapping_tb()

    for storecategory_name, advertiser_category_name, network in mappingObj.mappings:
        print(f"advertiser_category_name={advertiser_category_name} -> storecategory_name={storecategory_name} -> network={network}")


if __name__ == "__main__":
    main()
