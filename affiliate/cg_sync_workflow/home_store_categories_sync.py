import logging
import time

from affiliate.cj.db_ops import create_advertisers_view as create_cj_advertisers_view
from affiliate.flexoffers.db_ops import create_advertisers_view as create_flexoffers_advertisers_view
from common.db_utils import DbUtils
from common.env import ENV_DEV, ENV_PROD

"""
Sync workflow that sends store-storecategory relationships data from affiliates to CashbackGoat DB
- Target table: public.home_store_categories
- Target db: local and prod
"""

# get the fully-qualified logger (here: `root.__main__`)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

CJ = 'cj'
FLEXOFFERS = 'flexoffers'
IMPACT = 'impact'


def sync_store_categories_to_cashbackgoat_stores(source_db_utils, target_db_utils):
    """
    Sync affiliate store-storecategory relationships data to CashbackGoat database

    Args:
        source_db_utils: Source database connection (which is dev db and where affiliate data is stored)
        target_db_utils: Target database connection (CashbackGoat DB to update)
    """
    logger.info(f"Syncing store-storecategory relationships to CashbackGoat {target_db_utils.env} DB")
    create_cj_advertisers_view(source_db_utils)
    create_flexoffers_advertisers_view(source_db_utils)

    # Get store_name-storecategory_name pairs from all affiliates
    relationships = source_db_utils.run_select_query("""
                                                     select distinct a.store_name,
                                                                     cat_name as storecategory_name
                                                     from scraped.aff__flexoffers_advertisers_view a
                                                              JOIN LATERAL UNNEST(a.storecategory_names) AS cat_name ON TRUE
                                                     WHERE application_status = 'Approved'
                                                       AND store_name Is NOT NULL
                                                       AND store_name != ''

                                                     UNION ALL

                                                     SELECT distinct a.store_name,
                                                                     a.parent_storecategory_name as storecategory_name
                                                     FROM scraped.aff__cj_advertisers_view a
                                                     WHERE relationship_status = 'joined'
                                                       and parent_storecategory_name is not null
                                                       AND store_name Is NOT NULL
                                                       AND store_name != ''

                                                     UNION ALL

                                                     SELECT distinct a.store_name,
                                                                     a.child_storecategory_name as storecategory_name
                                                     FROM scraped.aff__cj_advertisers_view a
                                                     WHERE relationship_status = 'joined'
                                                       and child_storecategory_name is not null
                                                       AND store_name Is NOT NULL
                                                       AND store_name != ''


                                                     """)

    logger.info(f"Found {len(relationships)} store_name-storecategory_name pairs")

    # Create a temporary table in the target DB to hold the data
    target_db_utils.execute_query("""
                                  DROP TABLE IF EXISTS temp_affiliate_store_categories;
                                  CREATE TEMP TABLE temp_affiliate_store_categories (
                                      store_name varchar(100), storecategory_name varchar(100)
                                  )
                                  """)

    # Insert data into temp table
    for r in relationships:
        store_name = r[0]
        storecategory_name = r[1]

        target_db_utils.execute_query("""
                                      INSERT INTO temp_affiliate_store_categories
                                          (store_name, storecategory_name)
                                      VALUES (%s, %s)
                                      """,
                                      (store_name, storecategory_name))

    # Insert new store-storecategory relationships (and skip existing ones)
    inserted = target_db_utils.execute_query(f"""
                                            INSERT INTO public.home_store_categories (store_id, storecategory_id)
                                            SELECT DISTINCT hs.id as store_id,
                                                            hsc.id as storecategory_id
                                            FROM temp_affiliate_store_categories t
                                                 JOIN public.home_store hs ON hs.name = t.store_name
                                                 JOIN public.home_storecategory hsc ON hsc.name = t.storecategory_name
                                            ON CONFLICT (store_id, storecategory_id) DO NOTHING
                                             """)

    # The execute_query method returns an integer count of affected rows
    inserted_count = inserted  # Just use the integer directly
    logger.info(f"Inserted {inserted_count} new store_name-storecategory_name pairs")

    # Clean up
    target_db_utils.execute_query("DROP TABLE IF EXISTS temp_affiliate_stores")

    return inserted_count


def main():
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)
    start_time = time.time()

    # set up connections to dev and prod DBs
    dev_db_utils = DbUtils(env=ENV_DEV)
    prod_db_utils = DbUtils(env=ENV_PROD)

    try:
        # Sync to local Dev Django DB
        logger.info(f"Syncing to CG {ENV_DEV} Django DB...")
        local_inserted = sync_store_categories_to_cashbackgoat_stores(dev_db_utils, dev_db_utils)
        logger.info(f"CG {ENV_DEV} DB:{local_inserted} inserted")

        # # Sync to production Django DB
        # logger.info(f"Syncing to CG {ENV_PROD} Django DB...")
        # prod_updated, prod_inserted = sync_flexoffers_to_cashbackgoat(dev_db_utils, prod_db_utils)
        # logger.info(f"CG {ENV_PROD} DB: {prod_updated} updated, {prod_inserted} inserted")
        #
        # # Verify that both databases were updated identically
        # if local_updated == prod_updated and local_inserted == prod_inserted:
        #     logger.info("✅ Both databases updated identically")
        # else:
        #     logger.warning("⚠️ Databases were not updated identically!")

    finally:
        # Close all database connections
        dev_db_utils.close()
        prod_db_utils.close()

    elapsed_time = time.time() - start_time
    logger.info(f"Operation completed in {elapsed_time:.2f} seconds")


if __name__ == "__main__":
    main()
