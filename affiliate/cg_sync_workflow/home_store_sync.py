import logging
import time

from slugify import slugify

from affiliate.cj.db_ops import create_advertisers_view as create_cj_advertisers_view
from affiliate.flexoffers.db_ops import create_advertisers_view as create_flexoffers_advertisers_view
from common.db_utils import DbUtils
from common.env import ENV_DEV, ENV_PROD

"""
Sync workflow that sends store datafrom affiliates to CashbackGoat DB
- Target table: public.home_store
- Target db: local and prod
"""
# get the fully-qualified logger (here: `root.__main__`)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

CJ = 'cj'
FLEXOFFERS = 'flexoffers'
IMPACT = 'impact'


def sync_advertisers_to_cashbackgoat_stores(source_db_utils, target_db_utils):
    """
    Sync affiliate advertisers data to CashbackGoat database

    Args:
        source_db_utils: Source database connection (which is dev db and where affiliate data is stored)
        target_db_utils: Target database connection (CashbackGoat DB to update)
    """
    logger.info(f"Syncing affiliate advertisers to CashbackGoat DB")
    create_cj_advertisers_view(source_db_utils)
    create_flexoffers_advertisers_view(source_db_utils)

    # Get advertisers from all affiliates
    advertisers = source_db_utils.run_select_query("""
                                                   SELECT advertiser_id,
                                                          advertiser_name,
                                                          store_name,
                                                          domain_url,
                                                          image_url,
                                                          'flexoffers' AS affiliate_network,
                                                          affiliate_url,
                                                          created_at,
                                                          updated_at
                                                   FROM scraped.aff__flexoffers_advertisers_view
                                                   WHERE application_status = 'Approved'
                                                     AND store_name Is NOT NULL
                                                     AND store_name != ''

                                                   UNION ALL
                                                   -- TODO: FIX THIS SKELETON CODE
                                                   SELECT advertiser_id,
                                                          advertiser_name,
                                                          store_name,
                                                          program_url AS domain_url,
                                                          NULL        AS image_url,
                                                          'cj'        AS affiliate_network,
                                                          affiliate_url,
                                                          created_at,
                                                          updated_at
                                                   FROM scraped.aff__cj_advertisers_view
                                                   WHERE relationship_status = 'joined'
                                                     AND store_name Is NOT NULL
                                                     AND store_name != ''

                                                   """)

    logger.info(f"Found {len(advertisers)} approved affiliate advertisers")

    # Create a temporary table in the target DB to hold the data
    target_db_utils.execute_query("""
                                  CREATE TEMP TABLE temp_affiliate_stores (
                                      advertiser_id     VARCHAR(255),
                                      advertiser_name   VARCHAR(255),
                                      store_name        VARCHAR(255) not null unique,
                                      slug              VARCHAR(255) not null unique,
                                      domain_url        VARCHAR(255),
                                      image_url         VARCHAR(255),
                                      icon_file         VARCHAR(255),
                                      affiliate_network VARCHAR(20),
                                      affiliate_url     TEXT,
                                      created_at        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                      updated_at        TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                                  )
                                  """)

    # Insert data into temp table
    for adv in advertisers:
        advertiser_id = adv[0]
        advertiser_name = adv[1]
        store_name = adv[2]
        slug = slugify(store_name)  # Generate slug using Python's slugify
        domain_url = adv[3]
        image_url = adv[4]
        affiliate_network = adv[5]
        affiliate_url = adv[6]
        created_at = adv[7]
        updated_at = adv[8]

        target_db_utils.execute_query("""
                                      INSERT INTO temp_affiliate_stores
                                      (advertiser_id, advertiser_name, store_name, slug, domain_url, image_url,
                                       icon_file, affiliate_network, affiliate_url, created_at, updated_at)
                                      VALUES (%s, %s, %s, %s, %s, %s, null, %s, %s, %s, %s)
                                      """,
                                      (advertiser_id, advertiser_name, store_name, slug, domain_url, image_url,
                                       affiliate_network, affiliate_url, created_at, updated_at))

    # Update existing stores
    updated = target_db_utils.execute_query(f"""
                                            UPDATE public.home_store hs
                                            SET name              = COALESCE(hs.name, t.store_name),
                                                display_name      = COALESCE(hs.display_name, t.store_name),
                                                slug              = COALESCE(hs.slug, t.slug),
                                                icon_url          = COALESCE(hs.icon_url, t.image_url),     -- prioritize existing icon_url
                                                icon_file         = COALESCE(hs.icon_file, t.icon_file),   -- prioritize existing icon_file
                                                affiliate_network = t.affiliate_network,
                                                affiliate_url     = t.affiliate_url,
                                                is_enabled        = TRUE,
                                                updated_at        = t.updated_at
                                            FROM temp_affiliate_stores t
                                            WHERE
                                                LOWER(hs.name) = LOWER(t.store_name) OR hs.slug = t.slug
                                            """)

    # The execute_query method returns an integer count of affected rows
    updated_count = updated  # Just use the integer directly
    logger.info(f"Updated {updated_count} existing stores")

    # Insert new stores
    inserted = target_db_utils.execute_query(f"""
                                             INSERT INTO public.home_store(name,
                                                                    display_name,
                                                                    slug,
                                                                    icon_url,
                                                                    icon_file,
                                                                    affiliate_network,
                                                                    affiliate_url,
                                                                    is_enabled,
                                                                    created_at,
                                                                    updated_at)
                                             SELECT t.store_name        AS name,
                                                    t.store_name        AS display_name,
                                                    t.slug              AS slug,
                                                    t.image_url         AS icon_url,
                                                    ''                  AS icon_file,
                                                    t.affiliate_network       AS affiliate_network,
                                                    t.affiliate_url     AS affiliate_url,
                                                    True                AS is_enabled,
                                                    t.created_at      AS created_at,
                                                    t.updated_at   AS updated_at
                                             FROM temp_affiliate_stores t
                                             WHERE NOT EXISTS (SELECT 1
                                                               FROM public.home_store hs
                                                               WHERE LOWER(hs.name) = LOWER(t.store_name) 
                                                                 OR hs.slug = t.slug
                                             )""")

    # The execute_query method returns an integer count of affected rows
    inserted_count = inserted  # Just use the integer directly
    logger.info(f"Inserted {inserted_count} new stores")

    # Clean up
    target_db_utils.execute_query("DROP TABLE IF EXISTS temp_affiliate_stores")

    return updated_count, inserted_count


def main():
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)
    start_time = time.time()

    # set up connections to dev and prod DBs
    dev_db_utils = DbUtils(env=ENV_DEV)
    prod_db_utils = DbUtils(env=ENV_PROD)

    try:
        # Sync to local Dev Django DB
        logger.info(f"Syncing to CG {ENV_DEV} Django DB...")
        local_updated, local_inserted = sync_advertisers_to_cashbackgoat_stores(dev_db_utils, dev_db_utils)
        logger.info(f"CG {ENV_DEV} DB: {local_updated} updated, {local_inserted} inserted")

        # # Sync to production Django DB
        # logger.info(f"Syncing to CG {ENV_PROD} Django DB...")
        # prod_updated, prod_inserted = sync_flexoffers_to_cashbackgoat(dev_db_utils, prod_db_utils)
        # logger.info(f"CG {ENV_PROD} DB: {prod_updated} updated, {prod_inserted} inserted")
        #
        # # Verify that both databases were updated identically
        # if local_updated == prod_updated and local_inserted == prod_inserted:
        #     logger.info("✅ Both databases updated identically")
        # else:
        #     logger.warning("⚠️ Databases were not updated identically!")

    finally:
        # Close all database connections
        dev_db_utils.close()
        prod_db_utils.close()

    elapsed_time = time.time() - start_time
    logger.info(f"Operation completed in {elapsed_time:.2f} seconds")


if __name__ == "__main__":
    main()
