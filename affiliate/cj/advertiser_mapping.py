# import csv
# import os
#
#
# class AdvertiserMapping:
#     def __init__(self,
#                  csv_path='/home/<USER>/Code/cashback_goat_deals_generator/affiliate/cj/data/aff__cj_store_mapping.csv'):
#         self.csv_path = csv_path
#         self.mapping = {}
#         self.ensure_file_exists()
#         self.load_mapping()
#
#     def ensure_file_exists(self):
#         """Create the CSV file if it doesn't exist"""
#         if not os.path.exists(os.path.dirname(self.csv_path)):
#             os.makedirs(os.path.dirname(self.csv_path))
#
#         # if not os.path.exists(self.csv_path):
#         #     with open(self.csv_path, 'w', newline='') as file:
#         #         writer = csv.writer(file)
#         #         writer.writerow(['advertiser_name', 'store_name'])
#
#     def load_mapping(self):
#         """Load the mapping from CSV into memory"""
#         try:
#             self.mapping = {}
#
#             with open(self.csv_path, 'r', newline='') as file:
#                 reader = csv.DictReader(file)
#                 for row in reader:
#                     if not row['store_name']:
#                         continue
#                     else:
#                         self.mapping[row['advertiser_name']] = row['store_name']
#
#             self.mapping[row['advertiser_name']] = row['store_name'] if row['store_name'] else None
#
#             print(f"Loaded {len(self.mapping)} advertiser mappings")
#         except Exception as e:
#             print(f"Error loading advertiser mapping: {e}")
#             self.mapping = {}
#
#     def get_store_name(self, advertiser_name):
#         """Get the store name for an advertiser name"""
#         return self.mapping.get(advertiser_name)
#
#     def add_or_update_mapping(self, advertiser_name, store_name):
#         """Add or update a mapping"""
#         try:
#             # Read existing data
#             rows = []
#             exists = False
#             idx = -1
#
#             with open(self.csv_path, 'r', newline='') as file:
#                 reader = csv.DictReader(file)
#                 for i, row in enumerate(reader):
#                     rows.append(dict(row))
#                     if row['advertiser_name'] == advertiser_name:
#                         exists = True
#                         idx = i
#
#             if exists:
#                 # Update existing mapping
#                 rows[idx]['store_name'] = store_name
#             else:
#                 # Add new mapping
#                 new_row = {
#                     'advertiser_name': advertiser_name,
#                     'store_name': store_name
#                 }
#                 rows.append(new_row)
#
#             # Write back to CSV
#             with open(self.csv_path, 'w', newline='') as file:
#                 fieldnames = ['advertiser_name', 'store_name']
#                 writer = csv.DictWriter(file, fieldnames=fieldnames)
#                 writer.writeheader()
#                 writer.writerows(rows)
#
#             # Reload the mapping
#             self.load_mapping()
#
#             return True
#         except Exception as e:
#             print(f"Error adding/updating mapping: {e}")
#             return False
#
#     def delete_mapping(self, advertiser_name):
#         """Delete a mapping"""
#         try:
#             # Read existing data
#             rows = []
#             exists = False
#             idx = -1
#
#             with open(self.csv_path, 'r', newline='') as file:
#                 reader = csv.DictReader(file)
#                 for i, row in enumerate(reader):
#                     rows.append(dict(row))
#                     if row['advertiser_name'] == advertiser_name:
#                         exists = True
#                         idx = i
#
#             if exists:
#                 # Remove the row
#                 rows.pop(idx)
#
#                 # Write back to CSV
#                 with open(self.csv_path, 'w', newline='') as file:
#                     fieldnames = ['advertiser_name', 'store_name']
#                     writer = csv.DictWriter(file, fieldnames=fieldnames)
#                     writer.writeheader()
#                     writer.writerows(rows)
#
#                 # Reload the mapping
#                 self.load_mapping()
#
#                 return True
#
#             return False
#         except Exception as e:
#             print(f"Error deleting mapping: {e}")
#             return False
#
#
# def main():
#     # Example usage
#     obj = AdvertiserMapping()
#
#     # Get all entries
#     mapping = obj.mapping
#     print(f">> Got {len(mapping)} valid entries..")
#
#     for key, value in mapping.items():
#         print(f"{key} -> {value}")
#
#     # # Add some example mappings
#     # mapping.add_or_update_mapping('ezTaxReturn.com', 'ezTaxReturn')
#     # mapping.add_or_update_mapping('Zwilling US', 'Zwilling')
#     #
#     # # Get a store name
#     # store_name = mapping.get_store_name('ezTaxReturn.com')
#     # print(f"Store for Advertiser ezTaxReturn.com': {store_name}")
#
#
# if __name__ == "__main__":
#     main()
