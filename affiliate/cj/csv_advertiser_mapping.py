# import os
# import csv
# from datetime import datetime
#
# class AdvertiserMapping:
#     def __init__(self, csv_path='data/aff__cj_store_mapping.csv'):
#         self.csv_path = csv_path
#         self.mapping = {}
#         self.ensure_file_exists()
#         self.load_mapping()
#
#     def ensure_file_exists(self):
#         """Create the CSV file if it doesn't exist"""
#         if not os.path.exists(os.path.dirname(self.csv_path)):
#             os.makedirs(os.path.dirname(self.csv_path))
#
#         if not os.path.exists(self.csv_path):
#             with open(self.csv_path, 'w', newline='') as file:
#                 writer = csv.writer(file)
#                 writer.writerow([
#                     'advertiser_name', 'store_name'
#                     # , 'advertiser_id', 'store_id',
#                     # 'notes', 'is_active', 'created_at', 'updated_at'
#                 ])
#
#     def load_mapping(self):
#         """Load the mapping from CSV into memory"""
#         try:
#             self.mapping = {}
#
#             with open(self.csv_path, 'r', newline='') as file:
#                 reader = csv.DictReader(file)
#                 for row in reader:
#                     # Only load active mappings
#                     if row['is_active'] == '1':
#                         self.mapping[row['advertiser_id']] = {
#                             'advertiser_name': row['advertiser_name'],
#                             'store_name': row['store_name'],
#                             'store_id': row['store_id'] if row['store_id'] else None,
#                             'notes': row['notes'] if row['notes'] else None
#                         }
#
#             print(f"Loaded {len(self.mapping)} advertiser mappings")
#         except Exception as e:
#             print(f"Error loading advertiser mapping: {e}")
#             self.mapping = {}
#
#     def get_store_name(self, advertiser_id):
#         """Get the store name for an advertiser ID"""
#         if advertiser_id in self.mapping:
#             return self.mapping[advertiser_id]['store_name']
#         return None
#
#     def get_store_info(self, advertiser_id):
#         """Get the complete store info for an advertiser ID"""
#         return self.mapping.get(advertiser_id)
#
#     def add_or_update_mapping(self, advertiser_id, advertiser_name, store_name,
#                               store_id=None, notes=None, is_active=True):
#         """Add or update a mapping"""
#         try:
#             # Read existing data
#             rows = []
#             exists = False
#             idx = -1
#
#             with open(self.csv_path, 'r', newline='') as file:
#                 reader = csv.DictReader(file)
#                 for i, row in enumerate(reader):
#                     rows.append(dict(row))
#                     if row['advertiser_id'] == advertiser_id:
#                         exists = True
#                         idx = i
#
#             now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
#
#             if exists:
#                 # Update existing mapping
#                 rows[idx]['advertiser_name'] = advertiser_name
#                 rows[idx]['store_name'] = store_name
#                 rows[idx]['store_id'] = store_id if store_id else ''
#                 rows[idx]['notes'] = notes if notes else ''
#                 rows[idx]['is_active'] = '1' if is_active else '0'
#                 rows[idx]['updated_at'] = now
#             else:
#                 # Add new mapping
#                 new_row = {
#                     'advertiser_id': advertiser_id,
#                     'advertiser_name': advertiser_name,
#                     'store_name': store_name,
#                     'store_id': store_id if store_id else '',
#                     'notes': notes if notes else '',
#                     'is_active': '1' if is_active else '0',
#                     'created_at': now,
#                     'updated_at': now
#                 }
#                 rows.append(new_row)
#
#             # Write back to CSV
#             with open(self.csv_path, 'w', newline='') as file:
#                 fieldnames = ['advertiser_id', 'advertiser_name', 'store_name', 'store_id',
#                               'notes', 'is_active', 'created_at', 'updated_at']
#                 writer = csv.DictWriter(file, fieldnames=fieldnames)
#                 writer.writeheader()
#                 writer.writerows(rows)
#
#             # Reload the mapping
#             self.load_mapping()
#
#             return True
#         except Exception as e:
#             print(f"Error adding/updating mapping: {e}")
#             return False
#
#     def delete_mapping(self, advertiser_id, soft_delete=True):
#         """Delete or deactivate a mapping"""
#         try:
#             # Read existing data
#             rows = []
#             exists = False
#             idx = -1
#
#             with open(self.csv_path, 'r', newline='') as file:
#                 reader = csv.DictReader(file)
#                 for i, row in enumerate(reader):
#                     rows.append(dict(row))
#                     if row['advertiser_id'] == advertiser_id:
#                         exists = True
#                         idx = i
#
#             if exists:
#                 if soft_delete:
#                     # Soft delete - just mark as inactive
#                     rows[idx]['is_active'] = '0'
#                     rows[idx]['updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
#                 else:
#                     # Hard delete - remove the row
#                     rows.pop(idx)
#
#                 # Write back to CSV
#                 with open(self.csv_path, 'w', newline='') as file:
#                     fieldnames = ['advertiser_id', 'advertiser_name', 'store_name', 'store_id',
#                                   'notes', 'is_active', 'created_at', 'updated_at']
#                     writer = csv.DictWriter(file, fieldnames=fieldnames)
#                     writer.writeheader()
#                     writer.writerows(rows)
#
#                 # Reload the mapping
#                 self.load_mapping()
#
#                 return True
#
#             return False
#         except Exception as e:
#             print(f"Error deleting mapping: {e}")
#             return False
#
#     def get_all_mappings(self, active_only=True):
#         """Get all mappings as a list of dictionaries"""
#         try:
#             mappings = []
#
#             with open(self.csv_path, 'r', newline='') as file:
#                 reader = csv.DictReader(file)
#                 for row in reader:
#                     if not active_only or row['is_active'] == '1':
#                         mappings.append(dict(row))
#
#             return mappings
#         except Exception as e:
#             print(f"Error getting all mappings: {e}")
#             return []
#
# def main():
#     # Example usage
#     mapping = AdvertiserMapping()
#
#     # Add some example mappings
#     mapping.add_or_update_mapping('1234567', 'Sample Advertiser', 'Sample Store')
#     mapping.add_or_update_mapping('7654321', 'Another Advertiser', 'Another Store')
#
#     # Get a store name
#     store_name = mapping.get_store_name('1234567')
#     print(f"Store for advertiser 1234567: {store_name}")
#
#     # Get all mappings
#     all_mappings = mapping.get_all_mappings()
#     print(f"All mappings ({len(all_mappings)}):")
#     for m in all_mappings:
#         print(f"{m['advertiser_name']} -> {m['store_name']}")
#
# if __name__ == "__main__":
#     main()