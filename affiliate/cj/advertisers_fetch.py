import concurrent.futures
import logging
import time

from dotenv import load_dotenv

from affiliate.cj.api_helper import fetch_objects_from_api
from affiliate.cj.db_ops import bulk_upsert_api_responses, create_advertisers_table, create_advertisers_view
from affiliate.common.db_util import create_html_decode_function
from common.db_utils import DbUtils
from common.env import ENV_DEV

"""
find advertisers based on CID, program name, or program URL
doc: https://developers.cj.com/docs/rest-apis/advertiser-lookup
curl -s -XGET "https://advertiser-lookup.api.cj.com/v2/advertiser-lookup?requestor-cid=7144689&advertiser-ids=joined" -H "Authorization: Bearer 54OWJiMyh7U1-63uqKsXs8Bjsw"
"""

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

ENDPOINT = 'advertiser-lookup'


def fetch_all_advertisers():
    """Fetch both joined and not joined advertisers in parallel"""
    url = f"https://advertiser-lookup.api.cj.com/v2/advertiser-lookup?requestor-cid=7144689&advertiser-ids="
    object_root_key = 'advertisers'
    object_child_key = 'advertiser'
    oject_id_key = 'advertiser-id'

    with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
        # Submit both API calls in parallel
        joined_future = executor.submit(fetch_objects_from_api, endpoint_url=url + "joined",
                                        object_root_key=object_root_key,
                                        object_child_key=object_child_key,
                                        oject_id_key=oject_id_key)
        notjoined_future = executor.submit(fetch_objects_from_api, endpoint_url=url + "notjoined",
                                           object_root_key=object_root_key,
                                           object_child_key=object_child_key,
                                           oject_id_key=oject_id_key)

        # Get results
        joined_advertisers = joined_future.result()
        notjoined_advertisers = notjoined_future.result()

    if joined_advertisers is None:
        logger.error("Failed to fetch joined advertisers")
        joined_advertisers = []

    if notjoined_advertisers is None:
        logger.error("Failed to fetch not joined advertisers")
        notjoined_advertisers = []

    # Combine results
    all_advertisers = joined_advertisers + notjoined_advertisers

    # Log the results with more detail
    logger.info(f"\nFinal counts:")
    logger.info(f"Total advertisers: {len(all_advertisers)}")
    logger.info(f"Joined advertisers: {len(joined_advertisers)}")
    logger.info(f"Not joined advertisers: {len(notjoined_advertisers)}")

    if len(all_advertisers) != len(joined_advertisers) + len(notjoined_advertisers):
        logger.warning(
            f"Warning: Count mismatch! Total should be {len(joined_advertisers) + len(notjoined_advertisers)}")

    return {
        "joined": joined_advertisers,
        "not_joined": notjoined_advertisers,
        "all": all_advertisers
    }


def load_raw_xml_to_advertisers_tb(dev_db_utils):
    """Process raw XML advertiser data into structured table format"""
    logger.info("Processing raw advertiser data into structured table")
    # Ensure the structured table exists
    create_advertisers_table(dev_db_utils)
    # Create HTML decode function
    create_html_decode_function(dev_db_utils)
    # Query scraped.aff__cj_api_responses table to get response_data for all advertisers then extract attributes in order to insert into scraped.aff__cj_advertisers table
    dev_db_utils.execute_query(f"""
        INSERT INTO scraped.aff__cj_advertisers (  advertiser_id,
                                              advertiser_name,
                                              account_status,
                                              program_url,
                                              relationship_status,
                                              seven_day_epc,
                                              three_month_epc,
                                              language,
                                              mobile_supported,
                                              mobile_tracking_certified,
                                              cookieless_tracking_enabled,
                                              network_rank,
                                              primary_category_parent,
                                              primary_category_child,
                                              performance_incentives,
                                              link_types,
                                              created_at,
                                              updated_at)
        SELECT
            (xpath('//advertiser//advertiser-id/text()', response_data))[1]::text                      AS advertiser_id,
            (xpath('//advertiser//advertiser-name/text()', response_data))[1]::text                                AS advertiser_name ,
            (xpath('//advertiser//account-status/text()', response_data))[1]::text                              AS  account_status ,
            (xpath('//advertiser//program-url/text()', response_data))[1]::text                              AS  program_url ,
            (xpath('//advertiser//relationship-status/text()', response_data))[1]::text                                AS relationship_status ,
            (xpath('//advertiser//seven-day-epc/text()', response_data))[1]::text                              AS seven_day_epc ,
            (xpath('//advertiser//three-month-epc/text()', response_data))[1]::text                             AS three_month_epc ,
            (xpath('//advertiser//language/text()', response_data))[1]::text                            AS language,
            (xpath('//advertiser//mobile-supported/text()', response_data))[1]::text::boolean                        AS  mobile_supported ,
            (xpath('//advertiser//mobile-tracking-certified/text()', response_data))[1]::text::boolean                         AS mobile_tracking_certified ,
            (xpath('//advertiser//cookieless-tracking-enabled/text()', response_data))[1]::text::boolean                     AS      cookieless_tracking_enabled ,
            (xpath('//advertiser//network-rank/text()', response_data))[1]::text                        AS   network_rank ,
            decode_html_entities((xpath('//advertiser//primary-category//parent/text()', response_data))[1]::text)                AS           primary_category_parent ,
            decode_html_entities((xpath('//advertiser//primary-category//child/text()', response_data))[1]::text)                  AS         primary_category_child ,
            (xpath('//advertiser//performance-incentives/text()', response_data))[1]::text::boolean              AS            performance_incentives ,
            ARRAY(
                    SELECT string_agg(x::text, ', ')
                    FROM unnest(xpath('//link-type/text()', response_data)) AS x
            )             AS      link_types ,
            CURRENT_TIMESTAMP AS created_at,
            CURRENT_TIMESTAMP AS updated_at
        FROM scraped.aff__cj_api_responses
        WHERE endpoint = '{ENDPOINT}'
        ON CONFLICT (advertiser_id) DO UPDATE 
            SET
                  advertiser_name = EXCLUDED.advertiser_name,
                  account_status = EXCLUDED.account_status,
                  program_url = EXCLUDED.program_url,
                  relationship_status = EXCLUDED.relationship_status,
                  seven_day_epc = EXCLUDED.seven_day_epc,
                  three_month_epc = EXCLUDED.three_month_epc,
                  language = EXCLUDED.language,
                  mobile_supported = EXCLUDED.mobile_supported,
                  mobile_tracking_certified = EXCLUDED.mobile_tracking_certified,
                  cookieless_tracking_enabled = EXCLUDED.cookieless_tracking_enabled,
                  network_rank = EXCLUDED.network_rank,
                  primary_category_parent = EXCLUDED.primary_category_parent,
                  primary_category_child = EXCLUDED.primary_category_child,
                  performance_incentives = EXCLUDED.performance_incentives,
                  link_types = EXCLUDED.link_types,
                  updated_at = EXCLUDED.updated_at
    """)

    # Get count of processed records
    result = dev_db_utils.run_select_query("SELECT COUNT(*) FROM scraped.aff__cj_advertisers")
    count = result[0][0] if result else 0

    logger.info(f"Successfully processed {count} advertisers into structured table")

    return count


def main():
    """Main function to fetch and store advertisers data"""
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)
    start_time = time.time()
    load_dotenv()
    dev_db_utils = DbUtils(env=ENV_DEV)

    # step 1: Fetch advertisers (both joined and not joined)
    objects_id_and_response = fetch_all_advertisers()['all']
    if not objects_id_and_response:
        logger.error("Failed to fetch advertisers")
        return

    # step 2: Store all advertisers xml in the database using bulk operation
    bulk_upsert_api_responses(dev_db_utils=dev_db_utils, objects_id_and_response=objects_id_and_response,
                              endpoint=ENDPOINT)
    logger.info(f"Total advertisers received from API call: {len(objects_id_and_response)}")

    # step 3: Process raw xml into structured table
    load_raw_xml_to_advertisers_tb(dev_db_utils)

    # step 4: (re)Create view for advertisers
    create_advertisers_view(dev_db_utils)

    elapsed_time = time.time() - start_time
    logger.info(f"\nOperation completed in {elapsed_time:.2f} seconds")
    dev_db_utils.close()


if __name__ == "__main__":
    main()
