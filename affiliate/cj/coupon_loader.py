"""Load CJ coupons data into cg home_storecoupon table"""

import logging
import time
from datetime import datetime

from common.db_utils import DbUtils
from common.env import ENV_DEV, ENV_PROD
from affiliate.cj import advertiser_links_fetch

# get the fully-qualified logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)



def get_store_id_name_mapping(db_utils):
    """Get mapping of store names to IDs from home_store table"""
    store_query = "SELECT id, name FROM home_store"
    stores = db_utils.run_select_query_dict(store_query)

    # Create a dictionary mapping store names to IDs
    store_mapping = {}
    for store in stores:
        store_id, store_name = store
        store_mapping[store['name'].lower()] =  store['id']

    return store_mapping


def get_store_coupons(dev_db_utils):
    """Fetch CJ eligible coupon datar"""

    # Fetch coupons from CJ data
    select_coupon_query = """
                          WITH cte AS (
                              SELECT link_id,
                                     advertiser_id,
                                     advertiser_name,
                                     store_name,
                                     coupon_code,
                                     REPLACE(link_name, ' ' || coupon_code, '') AS link_name, -- redact coupon_code from link_name
                                     REPLACE(description, ' ' || coupon_code, '') AS description, -- redact coupon_code from description
                                     click_url,
                                     promotion_start_date,
                                     promotion_end_date,
                                     created_at,
                                     row_number() over (PARTITION BY advertiser_id, coupon_code  ORDER BY last_updated desc) as row_num
                              FROM scraped.aff__cj_advertiser_links
                              WHERE relationship_status = 'joined'
                                AND store_name IS NOT NULL
                                AND COALESCE(language, '') = 'English'
                                AND (array_length(targeted_countries, 1) is null OR 'US' = ANY (targeted_countries))
                                AND promotion_type = 'Coupon'
                                AND COALESCE(coupon_code, '') != ''
                              AND COALESCE(promotion_end_date, NOW()) >= NOW()
                          )
                          SELECT * FROM cte WHERE row_num = 1

                          """

    coupons_data = dev_db_utils.run_select_query_dict(select_coupon_query)
    logger.info(f"Found {len(coupons_data)} CJ coupons to process")
    return coupons_data

def clean_coupon_title(title):
    """Remove image size patterns like '160x800' or '1024x680' from text
    Args:
        title (str): The input text containing image size patterns
    Returns:
        str: Title with image size patterns removed
    """
    import re

    if not title:
        return title

    # Pattern matches common image size formats (e.g., 160x800, 1024x680)
    # The pattern looks for:
    # - Numbers (1 or more digits)
    # - Followed by 'x' or 'X'
    # - Followed by more numbers (1 or more digits)
    pattern = r'\b\d+[xX]\d+\b'

    # Replace all occurrences with empty string
    cleaned_title = re.sub(pattern, '', title)
    # Remove any double spaces that might have been created
    cleaned_title = re.sub(r'\s+', ' ', cleaned_title).strip()
    return cleaned_title

def get_mapped_store_coupons(coupon_records, db_utils, processing_ts=datetime.now()):
    """add store_id to coupon_records so it could connect with home_store table"""

    # Get store id-mapping from db of this env
    store_id_name_mapping = get_store_id_name_mapping(db_utils)
    logger.info(f"Got mapping of {len(store_id_name_mapping)} store_id-store_name in {db_utils.env} database")

    # Prepare data for insertion
    mapped_coupon_records = []
    skipped_count = 0
    for coupon in coupon_records:
        try:
            store_name = coupon['store_name']
            if not store_name:
                logger.warning(f"No store name mapping for advertiser {coupon['advertiser_name']}")
                skipped_count += 1
                continue

            # Look up store ID in our mapping
            store_id = store_id_name_mapping.get(store_name.lower())
            if not store_id:
                logger.warning(f"No home_store record found for store: {store_name}")
                skipped_count += 1
                continue

            # Add to our records list
            mapped_coupon_records.append({
                'store_id': store_id,
                'code': coupon['coupon_code'][:100],
                'title': clean_coupon_title(coupon['link_name'])[:100],
                'description': coupon['description'],
                'click_url': coupon['click_url'][:500],
                'ext_source': 'cj',
                'ext_object_id': coupon['link_id'],
                'status': 'active',
                'start_datetime': coupon['promotion_start_date'],
                'end_datetime': coupon['promotion_end_date'],
                'created_at': coupon['created_at'],
                'updated_at': processing_ts
            })

        except Exception as e:
            logger.error(f"Error processing coupon for {coupon.get('advertiser_name', 'Unknown')}: {str(e)}")
            skipped_count += 1

    logger.info(f"Prepared {len(mapped_coupon_records)} mapped coupons for UPSERTING, skipped {skipped_count}")
    return mapped_coupon_records


def upsert_store_coupons(coupon_records, db_utils):
    """Upsert coupon_records into the Store Coupon table"""

    # Now upsert the records into the production database
    if coupon_records:

        # Keep track of valid link_ids for later deletion of outdated coupons
        valid_link_ids = {record['ext_object_id'] for record in coupon_records}

        # First, create a temporary table to hold our new data
        a= """"""
        create_temp_table_query = """
      CREATE TEMP TABLE temp_cj_coupons (
            store_id bigint,
            code VARCHAR(100),
            title TEXT,
            description TEXT,
            click_url VARCHAR(500),
            ext_source varchar,
            ext_object_id varchar(100),
            status VARCHAR(10),
            start_datetime TIMESTAMP WITH TIME ZONE,
            end_datetime TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE,
            updated_at TIMESTAMP WITH TIME ZONE
        ) 
        """
        db_utils.execute_query(create_temp_table_query)

        # Insert data into temporary table
        insert_temp_query = """
        INSERT INTO temp_cj_coupons (store_id,
                                     code,
                                     title,
                                     description,
                                     click_url,
                                     ext_source,
                                     ext_object_id,

                                     status,
                                     start_datetime,
                                     end_datetime,
                                     created_at,
                                     updated_at)
        VALUES (%(store_id)s, %(code)s, %(title)s, %(description)s, %(click_url)s, 
                %(ext_source)s, %(ext_object_id)s, %(status)s,
                %(start_datetime)s, %(end_datetime)s, %(created_at)s, %(updated_at)s)
        """
        db_utils.execute_query_in_batch(insert_temp_query, coupon_records)

        # Perform the upsert operation
        upsert_query = """
        INSERT INTO home_storecoupon (store_id,
                                     code,
                                      title,
                                     description,
                                     click_url,
                                     ext_source,
                                     ext_object_id,
                                     status,
                                     start_datetime,
                                     end_datetime,
                                     created_at,
                                     updated_at)
        SELECT store_id,
              code,
               title,
              description,
              click_url,
              ext_source,
              ext_object_id,
              status,
              start_datetime,
              end_datetime,
              created_at,
              updated_at
        FROM temp_cj_coupons ON CONFLICT (store_id, ext_source, ext_object_id) 
        DO
        UPDATE SET
           code = EXCLUDED.code,
            title = EXCLUDED.title,
           description = EXCLUDED.description,
           click_url = EXCLUDED.click_url,
           status = EXCLUDED.status,
           start_datetime = EXCLUDED.start_datetime,
           end_datetime = EXCLUDED.end_datetime,
           updated_at = EXCLUDED.updated_at
           RETURNING id 
        """

        # Execute the upsert and count affected rows
        result = db_utils.execute_query(upsert_query)
        inserted_count = result

        logger.info(f"Successfully upserted {inserted_count} CJ coupons")

        # Clean up temp table
        db_utils.execute_query("DROP TABLE IF EXISTS temp_cj_coupons")

        # Delete outdated coupons (those with ext_source='cj' but ext_object_id not in valid_link_ids)
        if valid_link_ids:
            # Convert set to list for SQL query
            valid_link_ids_list = list(valid_link_ids)

            # Prepare the query with placeholders
            placeholders = ','.join(['%s'] * len(valid_link_ids_list))
            delete_query = f"""
            DELETE FROM home_storecoupon
            WHERE ext_source = 'cj'
            AND ext_object_id NOT IN ({placeholders})
            RETURNING id
            """

            result = db_utils.execute_query(delete_query, valid_link_ids_list)
            deleted_count = result

            logger.info(f"Deleted {deleted_count} outdated CJ coupons")
        else:
            logger.warning("No valid link IDs found to verify outdated coupons")

        return inserted_count

    return 0


def main():
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)
    start_time = time.time()
    processing_ts = datetime.now()

    dev_db_utils = DbUtils(env=ENV_DEV)  # For reading CJ data
    # TODO change back to ENV_PROD
    prod_db_utils = DbUtils(env=ENV_PROD)  # For writing to store coupon table

    # step 0 (optional): pull the latest coupon from api and update local db
    # advertiser_links_fetch.main()


    # step 1: get all coupons data
    logger.info("Getting all CJ coupons data")
    coupon_records = get_store_coupons(dev_db_utils)

    # step 2: load to dev db
    # step 2a: enriched coupons data with store_id
    mapped_coupon_records = get_mapped_store_coupons(coupon_records, dev_db_utils, processing_ts)
    # step 2b: upsert coupons data into dev home_storecoupon table
    logger.info(f"> Upserting {dev_db_utils.env} store coupons from CJ data")
    count = upsert_store_coupons(mapped_coupon_records, dev_db_utils)

    # step 3: load to prod db
    # step 3a: enriched coupons data with store_id
    mapped_coupon_records = get_mapped_store_coupons(coupon_records, prod_db_utils, processing_ts)
    # step 3b: upsert coupons data into dev home_storecoupon table
    logger.info(f"> Upserting {prod_db_utils.env}  store coupons from CJ data")
    count = upsert_store_coupons(mapped_coupon_records, prod_db_utils)

    elapsed_time = time.time() - start_time
    logger.info(f"Operation completed in {elapsed_time:.2f} seconds")
    logger.info(f"Total coupons processed : {count}")

    # Close database connections
    dev_db_utils.close()
    prod_db_utils.close()


if __name__ == "__main__":
    main()
