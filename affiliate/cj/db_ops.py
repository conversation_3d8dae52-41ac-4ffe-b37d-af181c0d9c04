import logging

from affiliate.common.advertiser_to_store import AdvertiserToStore

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def create_api_responses_table(dev_db_utils):
    """Create the generalized API response table"""
    dev_db_utils.execute_query("""
                               CREATE TABLE IF NOT EXISTS scraped.aff__cj_api_responses (
                                   id            SERIAL PRIMARY KEY,
                                   object_id     VARCHAR(50) NOT NULL,
                                   endpoint      VARCHAR(50) NOT NULL,
                                   response_data XML         NOT NULL,
                                   created_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   UNIQUE (object_id, endpoint)
                               );
                               """)


def bulk_upsert_api_responses(dev_db_utils, objects_id_and_data, endpoint):
    """Store objects of various types and their raw json in the db"""
    # Ensure the table exists
    create_api_responses_table(dev_db_utils)

    # Use ON CONFLICT for PostgreSQL 9.5+ (upsert) with error handling
    upsert_cnt = dev_db_utils.execute_query_in_batch(query=f"""
                                              INSERT INTO scraped.aff__cj_api_responses (
                                                                                            object_id,
                                                                                            endpoint,
                                                                                            response_data,
                                                                                            created_at,
                                                                                            updated_at)
                                              VALUES ( %s, '{endpoint}', %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                                                  ON CONFLICT (object_id, endpoint) DO UPDATE
                                                   SET
                                                       response_data = EXCLUDED.response_data,
                                                        updated_at = CURRENT_TIMESTAMP
                                            WHERE scraped.aff__cj_api_responses.response_data::text IS DISTINCT FROM EXCLUDED.response_data::text
                                              """, argslist=objects_id_and_data)

    logger.info(f"Successfully upserted {upsert_cnt} objects in table: scraped.aff__cj_api_responses")


def create_advertisers_table(dev_db_utils):
    """Create the advertisers table if it doesn't exist"""
    dev_db_utils.execute_query("""
                               CREATE TABLE IF NOT EXISTS scraped.aff__cj_advertisers (
                                   advertiser_id               VARCHAR(50) PRIMARY KEY,
                                   advertiser_name             VARCHAR(255) NOT NULL,
                                   account_status              VARCHAR(50),
                                   program_url                 TEXT,
                                   relationship_status         VARCHAR(50),
                                   seven_day_epc               TEXT, --  non-numeric values exist
                                   three_month_epc             TEXT, --  non-numeric values exist
                                   language                    VARCHAR(10),
                                   mobile_supported            BOOLEAN,
                                   mobile_tracking_certified   BOOLEAN,
                                   cookieless_tracking_enabled BOOLEAN,
                                   network_rank                TEXT,
                                   primary_category_parent     VARCHAR(100),
                                   primary_category_child      VARCHAR(100),
                                   performance_incentives      BOOLEAN,
                                   link_types                  TEXT[],
                                   created_at                  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_at                  TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                               )
                               """)


def create_advertiser_links_table(dev_db_utils):
    """Create the advertiser links table if it doesn't exist"""
    logger.info(f"Created table = scraped.aff__cj_advertiser_links")
    dev_db_utils.execute_query("""
                               CREATE TABLE IF NOT EXISTS scraped.aff__cj_advertiser_links
                               (
                                   id                    SERIAL PRIMARY KEY,
                                   advertiser_id         TEXT NOT NULL,
                                   advertiser_name       TEXT NOT NULL,
                                   category              TEXT,
                                   click_commission      FLOAT,
                                   creative_height       INTEGER,
                                   creative_width        INTEGER,
                                   language              TEXT,
                                   lead_commission       TEXT,
                                   destination           TEXT,
                                   link_id               TEXT NOT NULL,
                                   link_name             TEXT,
                                   description           TEXT,
                                   link_type             TEXT,
                                   allow_deep_linking    BOOLEAN,
                                   performance_incentive BOOLEAN,
                                   promotion_end_date    timestamp with time zone,
                                   promotion_start_date  timestamp with time zone,
                                   promotion_type        TEXT,
                                   coupon_code           TEXT,
                                   relationship_status   TEXT,
                                   sale_commission       TEXT,
                                   mobile_optimized      BOOLEAN,
                                   mobile_app_download   BOOLEAN,
                                   cross_device_only     BOOLEAN,
                                   targeted_countries    TEXT[],
                                   event_name            TEXT,
                                   ad_content            TEXT,
                                   last_updated          TEXT,
                                   seven_day_epc         FLOAT,
                                   three_month_epc       FLOAT,
                                   click_url             TEXT,
                                   created_at            TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   UNIQUE (advertiser_id, link_id)
                               )
                               """)


def create_advertisers_view(dev_db_utils):
    """Create the advertisers view if it doesn't exist"""
    # TODO: better to refresh deps data first:
    # 1. update scraped.aff__store_mapping:
    #   a. update csv 2. run advertiser_store_mapping.py
    # 2. update scraped.cj_categories
    #   a. update csv 2. run categories_fetch.py
    # AdvertiserToStore().create_affiliate_store_mapping_tb()

    # Create the view using the temporary table
    dev_db_utils.execute_query("""
                               CREATE OR REPLACE VIEW scraped.aff__cj_advertisers_view AS
                               WITH links AS (SELECT DISTINCT ON (advertiser_id) advertiser_id,
                                                                                 click_url
                                              FROM scraped.aff__cj_advertiser_links
                                              WHERE click_url IS NOT NULL
                                                AND click_url != ''
                                                AND relationship_status = 'joined'
                                                AND LANGUAGE = 'English'
                                                AND (promotion_start_date <= NOW() OR promotion_start_date IS NULL)
                                                AND (promotion_end_date >= NOW() + INTERVAL '3 day' OR
                                                     promotion_end_date IS NULL)
                                              ORDER BY advertiser_id, promotion_end_date DESC)
                               SELECT a.*,
                                      sm.store_name         AS store_name,
                                      links.click_url       AS affiliate_url,
                                      img.image_url       AS image_url,
                                      cm1.storecategory_name AS parent_storecategory_name,
                                      cm2.storecategory_name AS child_storecategory_name
                               FROM scraped.aff__cj_advertisers a
                                        LEFT JOIN scraped.aff__store_mapping sm
                                                  ON a.advertiser_name = sm.advertiser_name AND sm.network = 'cj'
                                        LEFT JOIN scraped.aff__storecategory_mapping cm1
                                                  on lower(coalesce(a.primary_category_parent, '')) =
                                                     lower(cm1.advertiser_category_name) and cm1.network = 'cj'
                                        LEFT JOIN scraped.aff__storecategory_mapping cm2
                                                  on lower(coalesce( a.primary_category_child, '')) =
                                                     lower(cm2.advertiser_category_name) and cm2.network = 'cj'
                                        LEFT JOIN scraped.aff__advertiser_image img  
                                                  ON a.advertiser_id = img.advertiser_id and img.network = 'cj'
                                        INNER JOIN links ON a.advertiser_id = links.advertiser_id
                               WHERE a.account_status = 'Active'
                                 and a.relationship_status = 'joined'
                               """)
    logger.info(f"View updated: scraped.aff__cj_advertisers_view")
