"""
find a variety of links matching a desired criteria across all advertisers or a specific advertiser


Fetch a variety of links for all joined advertisers or a specific advertiser
link search api doc: https://developers.cj.com/docs/rest-apis/link-search

curl -s XGET "https://link-search.api.cj.com/v2/link-search?website-id=101203121&link-type=text%20link&advertiser-ids=1874913" -H "Authorization: Bearer 54OWJiMyh7U1-63uqKsXs8Bjsw"
curl -s XGET "https://link-search.api.cj.com/v2/link-search?website-id=101203121&link-type=text%20link&advertiser-ids=joined" -H "Authorization: Bearer 54OWJiMyh7U1-63uqKsXs8Bjsw"
curl -s XGET "https://link-search.api.cj.com/v2/link-search?website-id=101203121&link-type=EvergreenLink&advertiser-ids=6284905" -H "Authorization: Bearer 54OWJiMyh7U1-63uqKsXs8Bjsw"
"""
import logging
import os
import time
import xml.etree.ElementTree as ET
from datetime import datetime

import requests
from dotenv import load_dotenv

from affiliate.cj.db_ops import create_advertiser_links_table
from affiliate.cj.xml_helper import get_element_text, parse_bool, parse_float, parse_date, parse_int, parse_text_array, \
    parse_datetime
from common.db_utils import DbUtils
from common.env import ENV_DEV

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def fetch_all_links(dev_db_utils):
    """
    Fetch links for a specific advertiser or all joined advertisers
    link search api: https://developers.cj.com/docs/rest-apis/link-search
    Enumerations: https://developers.cj.com/docs/rest-apis/enumerations#link-type

    Link types include: Banner, Text Link, Content Link, EvergreenLink, DeepLink,
    Hybrid Link, Product Link, Simple Link, Auto-monetized Link, Smart Link
    """
    logger.info(f"> Fetching links for all joined advertisers")

    url = f"https://link-search.api.cj.com/v2/link-search?website-id=101203121&advertiser-ids=joined"
    headers = {
        "Authorization": f"Bearer {os.getenv('CJ_ACCESS_TOKEN')}",
        "Content-Type": "application/xml"
    }

    all_links = []
    page = 1
    total_pages = 1  # Will be updated from the first response
    per_page = 100  # API default

    links_stored = 0

    while page <= total_pages:
        paginated_url = f"{url}&page-number={page}"
        response = requests.get(paginated_url, headers=headers)

        if response.status_code != 200:
            logger.info(f"Failed to fetch links (page {page}): {response.status_code}")
            logger.info("Response content:", response.text[:500])
            return None

        try:
            # Parse the XML response
            root = ET.fromstring(response.content)

            # Extract pagination information
            links_elem = root.find('.//links')
            if links_elem is None:
                logger.info(f"No links found from url={paginated_url}")
                return []

            total_matched = int(links_elem.get('total-matched', 0))
            records_returned = int(links_elem.get('records-returned', 0))

            # Calculate total pages if this is the first page
            if page == 1:
                total_pages = (total_matched + per_page - 1) // per_page if total_matched > 0 else 0
                logger.info(f"Total links: {total_matched}, pages: {total_pages}")

            # If no links found
            if total_matched == 0:
                return []

            # Process links in this page
            for link in root.findall('.//link'):
                link_data = {
                    'advertiser-id': get_element_text(link, 'advertiser-id'),
                    'advertiser-name': get_element_text(link, 'advertiser-name'),
                    'category': get_element_text(link, 'category'),
                    'click-commission': parse_float(get_element_text(link, 'click-commission')),
                    'creative-height': parse_int(get_element_text(link, 'creative-height')),
                    'creative-width': parse_int(get_element_text(link, 'creative-width')),
                    'language': get_element_text(link, 'language'),
                    'lead-commission': get_element_text(link, 'lead-commission'),
                    'destination': get_element_text(link, 'destination'),
                    'link-id': get_element_text(link, 'link-id'),
                    'link-name': get_element_text(link, 'link-name'),
                    'description': get_element_text(link, 'description'),
                    'link-type': get_element_text(link, 'link-type'),
                    'allow-deep-linking': parse_bool(get_element_text(link, 'allow-deep-linking')),
                    'performance-incentive': parse_bool(get_element_text(link, 'performance-incentive')),
                    'promotion-end-date': parse_datetime(get_element_text(link, 'promotion-end-date')),
                    'promotion-start-date': parse_datetime(get_element_text(link, 'promotion-start-date')),
                    'promotion-type': get_element_text(link, 'promotion-type'),
                    'coupon-code': get_element_text(link, 'coupon-code'),
                    'relationship-status': get_element_text(link, 'relationship-status'),
                    'sale-commission': get_element_text(link, 'sale-commission'),
                    'mobile-optimized': parse_bool(get_element_text(link, 'mobile-optimized')),
                    'mobile-app-download': parse_bool(get_element_text(link, 'mobile-app-download')),
                    'cross-device-only': parse_bool(get_element_text(link, 'cross-device-only')),
                    'targeted-countries': parse_text_array(get_element_text(link, 'targeted-countries')),
                    'event-name': get_element_text(link, 'event-name'),
                    'ad-content': get_element_text(link, 'ad-content'),
                    'last-updated': parse_date(get_element_text(link, 'last-updated')),
                    'seven-day-epc': parse_float(get_element_text(link, 'seven-day-epc')),
                    'three-month-epc': parse_float(get_element_text(link, 'three-month-epc')),
                    'click-url': get_element_text(link, 'clickUrl')
                }

                all_links.append(link_data)

            logger.info(f"Processed links for all joined advertisers, page {page}/{total_pages}")
            page += 1

            # Add a small delay between API requests to avoid rate limiting
            if page <= total_pages:
                time.sleep(0.5)

        except ET.ParseError as e:
            logger.info(f"Error parsing XML for all joined advertisers: {e}")
            logger.info("Response content:", response.text[:500])
            return None
        except Exception as e:
            logger.info(f"Error processing links for all joined advertisers: {e}")
            return None

    logger.info(f"Fetched {links_stored} links for all joined advertisers")
    return all_links


def write_links_to_db(dev_db_utils, all_links):
    if len(all_links) < 1:
        return

    logger.debug(
        f'Inserting {len(all_links)} all scraped stores into temp stores table scraped.aff__cj_advertiser_links.')
    link_records = [(link_data['advertiser-id'],
                     link_data['advertiser-name'],
                     link_data['category'],
                     link_data['click-commission'],
                     link_data['creative-height'],
                     link_data['creative-width'],
                     link_data['language'],
                     link_data['lead-commission'],
                     link_data['destination'],
                     link_data['link-id'],
                     link_data['link-name'],
                     link_data['description'],
                     link_data['link-type'],
                     link_data['allow-deep-linking'],
                     link_data['performance-incentive'],
                     link_data['promotion-end-date'],
                     link_data['promotion-start-date'],
                     link_data['promotion-type'],
                     link_data['coupon-code'],
                     link_data['relationship-status'],
                     link_data['sale-commission'],
                     link_data['mobile-optimized'],
                     link_data['mobile-app-download'],
                     link_data['cross-device-only'],
                     link_data['targeted-countries'],
                     link_data['event-name'],
                     link_data['ad-content'],
                     link_data['last-updated'],
                     link_data['seven-day-epc'],
                     link_data['three-month-epc'],
                     link_data['click-url'],
                     datetime.now()) for link_data in all_links]
    insert_cj_advertiser_links_query = """INSERT INTO scraped.aff__cj_advertiser_links (advertiser_id,
                                                                                        advertiser_name,
                                                                                        category,
                                                                                        click_commission,
                                                                                        creative_height,
                                                                                        creative_width,
                                                                                        language,
                                                                                        lead_commission,
                                                                                        destination,
                                                                                        link_id,
                                                                                        link_name,
                                                                                        description,
                                                                                        link_type,
                                                                                        allow_deep_linking,
                                                                                        performance_incentive,
                                                                                        promotion_end_date,
                                                                                        promotion_start_date,
                                                                                        promotion_type,
                                                                                        coupon_code,
                                                                                        relationship_status,
                                                                                        sale_commission,
                                                                                        mobile_optimized,
                                                                                        mobile_app_download,
                                                                                        cross_device_only,
                                                                                        targeted_countries,
                                                                                        event_name,
                                                                                        ad_content,
                                                                                        last_updated,
                                                                                        seven_day_epc,
                                                                                        three_month_epc,
                                                                                        click_url,
                                                                                        created_at)
                                          VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                                                  %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                          ON CONFLICT (advertiser_id, link_id) DO UPDATE SET advertiser_name       = EXCLUDED.advertiser_name,
                                                                                             category              = EXCLUDED.category,
                                                                                             click_commission      = EXCLUDED.click_commission,
                                                                                             creative_height       = EXCLUDED.creative_height,
                                                                                             creative_width        = EXCLUDED.creative_width,
                                                                                             language              = EXCLUDED.language,
                                                                                             lead_commission       = EXCLUDED.lead_commission,
                                                                                             destination           = EXCLUDED.destination,
                                                                                             link_name             = EXCLUDED.link_name,
                                                                                             description           = EXCLUDED.description,
                                                                                             link_type             = EXCLUDED.link_type,
                                                                                             allow_deep_linking    = EXCLUDED.allow_deep_linking,
                                                                                             performance_incentive = EXCLUDED.performance_incentive,
                                                                                             promotion_end_date    = EXCLUDED.promotion_end_date,
                                                                                             promotion_start_date  = EXCLUDED.promotion_start_date,
                                                                                             promotion_type        = EXCLUDED.promotion_type,
                                                                                             coupon_code           = EXCLUDED.coupon_code,
                                                                                             relationship_status   = EXCLUDED.relationship_status,
                                                                                             sale_commission       = EXCLUDED.sale_commission,
                                                                                             mobile_optimized      = EXCLUDED.mobile_optimized,
                                                                                             mobile_app_download   = EXCLUDED.mobile_app_download,
                                                                                             cross_device_only     = EXCLUDED.cross_device_only,
                                                                                             targeted_countries    = EXCLUDED.targeted_countries,
                                                                                             event_name            = EXCLUDED.event_name,
                                                                                             ad_content            = EXCLUDED.ad_content,
                                                                                             last_updated          = EXCLUDED.last_updated,
                                                                                             seven_day_epc         = EXCLUDED.seven_day_epc,
                                                                                             three_month_epc       = EXCLUDED.three_month_epc,
                                                                                             click_url             = EXCLUDED.click_url \
                                       """
    dev_db_utils.execute_query_in_batch(query=insert_cj_advertiser_links_query, argslist=link_records)


def main():
    """Main function to fetch and store advertisers links data"""
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)
    start_time = time.time()
    load_dotenv()
    dev_db_utils = DbUtils(env=ENV_DEV)

    # Ensure the links table exists
    create_advertiser_links_table(dev_db_utils)

    # fetch links and write to db
    all_links = fetch_all_links(dev_db_utils)
    write_links_to_db(dev_db_utils, all_links)

    elapsed_time = time.time() - start_time
    logger.info(f"\nOperation completed in {elapsed_time:.2f} seconds")
    logger.info(f"Total links fetched and stored: {len(all_links)}")

    dev_db_utils.close()


if __name__ == "__main__":
    main()
