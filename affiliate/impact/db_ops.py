import logging

from affiliate.common.advertiser_to_store import AdvertiserToStore

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

def bulk_upsert_api_responses(dev_db_utils, objects_id_and_data, endpoint):
    """Store objects of various types and their raw json in the db"""
    # Ensure the table exists
    create_api_responses_table(dev_db_utils)

    # Use ON CONFLICT for PostgreSQL 9.5+ (upsert) with error handling
    upsert_cnt = dev_db_utils.execute_query_in_batch(query=f"""
                                              INSERT INTO scraped.aff__impact_api_responses (
                                                                                            object_id,
                                                                                            endpoint,
                                                                                            response_data,
                                                                                            created_at,
                                                                                            updated_at)
                                              VALUES ( %s, '{endpoint}', %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                                                  ON CONFLICT (object_id, endpoint) DO UPDATE
                                                   SET
                                                       response_data = EXCLUDED.response_data,
                                                   updated_at = CURRENT_TIMESTAMP
                                              """, argslist=objects_id_and_data)

    logger.info(f"Successfully upserted {upsert_cnt} objects in table: scraped.aff__impact_api_responses")


def create_api_responses_table(dev_db_utils):
    """Create the generalized API response table"""
    dev_db_utils.execute_query("""
                               CREATE TABLE IF NOT EXISTS scraped.aff__impact_api_responses (
                                   id            SERIAL PRIMARY KEY,
                                   object_id     VARCHAR(50) NOT NULL,
                                   endpoint      VARCHAR(50) NOT NULL,
                                   response_data JSONB       NOT NULL,
                                   created_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   UNIQUE (object_id, endpoint)
                               );
                               """)


def create_campaigns_table(dev_db_utils):
    """Create the campaigns table if it doesn't exist"""
    dev_db_utils.execute_query("""
                               CREATE TABLE IF NOT EXISTS scraped.aff__impact_campaigns (
                                   -- Primary identifiers
                                   advertiser_id           VARCHAR(50) NOT NULL,
                                   campaign_id             VARCHAR(50) PRIMARY KEY,  -- each advertiser_id can have multiple campaigns 

                                   -- Basic information
                                   advertiser_name         VARCHAR(255) NOT NULL,
                                   advertiser_url          TEXT,
                                   campaign_name           VARCHAR(255) NOT NULL,
                                   campaign_url            TEXT,
                                   campaign_description    TEXT,

                                   -- Media and branding
                                   campaign_logo_uri       TEXT,

                                   -- Contract and status information
                                   contract_status         VARCHAR(50),
                                   contract_uri            TEXT,
                                   public_terms_uri        TEXT,

                                   -- Tracking information
                                   tracking_link           TEXT,

                                   -- Features and capabilities
                                   has_stand_down_policy   BOOLEAN   DEFAULT FALSE,
                                   allows_deeplinking      BOOLEAN   DEFAULT FALSE,
                                   deeplink_domains        TEXT[],   -- Array of allowed deeplink domains

                                   -- Regional information
                                   shipping_regions        TEXT[],   -- Array of shipping regions

                                   -- API reference
                                   uri                     TEXT,     -- API URI reference

                                   -- Metadata
                                   created_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                               )
                               """)

def create_advertisers_view(dev_db_utils):
    """Create the advertisers table if it doesn't exist"""
    dev_db_utils.execute_query("""
                               CREATE OR REPLACE VIEW scraped.aff__impact_advertisers_view AS
                                   SELECT
                                       advertiser_id AS advertiser_id           ,
                                   -- Basic information
                                       advertiser_name AS advertiser_name     ,
                                       campaign_name as campaign_name,
                                       NULL AS domain_url          ,
                                   -- Media and branding
                                   NULL image_url                ,
                                       tracking_link AS affiliate_url            ,
                                  
                                   -- Status information
                                       shipping_regions AS region                  ,
                                       contract_status AS status                 ,
                                   -- Metadata
                                       created_at AS created_at           ,
                                   updated_at updated_at           
                               FROM scraped.aff__impact_campaigns
                               """)


