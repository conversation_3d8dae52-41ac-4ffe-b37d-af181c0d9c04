import logging
import time

from dotenv import load_dotenv

from affiliate.common.db_util import create_html_decode_function
from affiliate.impact.api_helper import fetch_objects_from_api
from affiliate.impact.db_ops import bulk_upsert_api_responses
from affiliate.impact.db_ops import create_campaigns_table, create_advertisers_view
from common.db_utils import DbUtils
from common.env import ENV_DEV

"""
/campaigns: Retrieve campaigns from impact, then store response json in scraped.aff__impact_api_responses
https://integrations.impact.com/impact-publisher/reference/campaigns-overview
example: 
 curl -X GET 'https://api.impact.com/Mediapartners/<AccountSID>Campaigns' \
  -u '<AccountSID>:<AuthToken>'' \
  -H 'Accept: application/json' 
"""

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

ENDPOINT = 'Campaigns'


def load_raw_json_to_campaigns_tb(dev_db_utils):
    """Process raw JSON campaigns data into structured table format
    note: category_names values in json response might be incomplete or have bad chars; use impact_categories.name instead
    """
    logger.info("Processing raw campaigns data into structured table")

    # Ensure the structured table exists
    create_campaigns_table(dev_db_utils)

    # Create HTML decode function
    create_html_decode_function(dev_db_utils)

    # Query to extract and transform data from the raw responses table
    dev_db_utils.execute_query("""
                               INSERT INTO scraped.aff__impact_campaigns (advertiser_id,
                                                                          campaign_id,
                                                                          advertiser_name,
                                                                          advertiser_url,
                                                                          campaign_name,
                                                                          campaign_url,
                                                                          campaign_description,
                                                                          campaign_logo_uri,
                                                                          contract_status,
                                                                          contract_uri,
                                                                          public_terms_uri,
                                                                          tracking_link,
                                                                          has_stand_down_policy,
                                                                          allows_deeplinking,
                                                                          deeplink_domains,
                                                                          shipping_regions,
                                                                          uri,
                                                                          created_at,
                                                                          updated_at)
                               SELECT response_data ->> 'AdvertiserId'        AS advertiser_id,
                                      response_data ->> 'CampaignId'          AS campaign_id,
                                      response_data ->> 'AdvertiserName'      AS advertiser_name,
                                      response_data ->> 'AdvertiserUrl'       AS advertiser_url,
                                      response_data ->> 'CampaignName'        AS campaign_name,
                                      response_data ->> 'CampaignUrl'         AS campaign_url,
                                      response_data ->> 'CampaignDescription' AS campaign_description,
                                      response_data ->> 'CampaignLogoUri'     AS campaign_logo_uri,
                                      response_data ->> 'ContractStatus'      AS contract_status,
                                      response_data ->> 'ContractUri'         AS contract_uri,
                                      response_data ->> 'PublicTermsUri'      AS public_terms_uri,
                                      response_data ->> 'TrackingLink'        AS tracking_link,
                                      CASE
                                          WHEN LOWER(response_data ->> 'HasStandDownPolicy') = 'true' THEN TRUE
                                          ELSE FALSE
                                          END                                 AS has_stand_down_policy,
                                      CASE
                                          WHEN LOWER(response_data ->> 'AllowsDeeplinking') = 'true' THEN TRUE
                                          ELSE FALSE
                                          END                                 AS allows_deeplinking,
                                      CASE
                                          WHEN jsonb_typeof(response_data -> 'DeeplinkDomains') = 'array' THEN
                                              ARRAY(SELECT jsonb_array_elements_text(response_data -> 'DeeplinkDomains'))
                                          ELSE NULL
                                          END                                 AS deeplink_domains,
                                      CASE
                                          WHEN jsonb_typeof(response_data -> 'ShippingRegions') = 'array' THEN
                                              ARRAY(SELECT jsonb_array_elements_text(response_data -> 'ShippingRegions'))
                                          ELSE NULL
                                          END                                 AS shipping_regions,
                                      response_data ->> 'Uri'                 AS uri,
                                      created_at                       AS created_at,
                                      updated_at                       AS updated_at
                               FROM scraped.aff__impact_api_responses
                               WHERE endpoint = 'Campaigns'
                               ON CONFLICT ( campaign_id) DO UPDATE SET advertiser_id         = EXCLUDED.advertiser_id,
                                                                        advertiser_name       = EXCLUDED.advertiser_name,
                                                                        advertiser_url        = EXCLUDED.advertiser_url,
                                                                        campaign_name         = EXCLUDED.campaign_name,
                                                                        campaign_url          = EXCLUDED.campaign_url,
                                                                        campaign_description  = EXCLUDED.campaign_description,
                                                                        campaign_logo_uri     = EXCLUDED.campaign_logo_uri,
                                                                        contract_status       = EXCLUDED.contract_status,
                                                                        contract_uri          = EXCLUDED.contract_uri,
                                                                        public_terms_uri      = EXCLUDED.public_terms_uri,
                                                                        tracking_link         = EXCLUDED.tracking_link,
                                                                        has_stand_down_policy = EXCLUDED.has_stand_down_policy,
                                                                        allows_deeplinking    = EXCLUDED.allows_deeplinking,
                                                                        deeplink_domains      = EXCLUDED.deeplink_domains,
                                                                        shipping_regions      = EXCLUDED.shipping_regions,
                                                                        uri                   = EXCLUDED.uri,
                                                                        updated_at            = EXCLUDED.updated_at
                               """)

    # Get count of processed records
    result = dev_db_utils.run_select_query("SELECT COUNT(*) FROM scraped.aff__impact_campaigns")
    count = result[0][0] if result else 0

    logger.info(f"Successfully processed {count} campaigns into structured table")

    return count


def main():
    """Main function to fetch and store campaigns data"""
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)
    start_time = time.time()
    load_dotenv()
    dev_db_utils = DbUtils(env=ENV_DEV)

    # step 1: Fetch all campaigns regardless of their status
    # objects_id_and_data = fetch_objects_from_api(endpoint=ENDPOINT, oject_id_key='CampaignId')
    #
    # if not objects_id_and_data:
    #     logger.error("Failed to fetch campaigns")
    #     return
    #
    # # step 2: Store all campaigns json in the database using bulk operation
    # bulk_upsert_api_responses(dev_db_utils=dev_db_utils, objects_id_and_data=objects_id_and_data,
    #                           endpoint=ENDPOINT)
    # logger.info(f"Total campaigns received from API call: {len(objects_id_and_data)}")

    # step 3: Process raw JSON into structured table
    # load_raw_json_to_campaigns_tb(dev_db_utils)

    # step 4: create_advertisers_view
    create_advertisers_view(dev_db_utils)

    elapsed_time = time.time() - start_time
    logger.info(f"\nOperation completed in {elapsed_time:.2f} seconds")
    dev_db_utils.close()


if __name__ == "__main__":
    main()
