"""api utils


"""

import concurrent.futures
import json
import logging
import os
import random
import time

import requests

from common.request_concurrent_util import create_session

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

IMPACT_BASE_URL = 'https://api.impact.com/Mediapartners'



def fetch_objects_from_api(endpoint='Campaigns', oject_id_key='id', pageSize=100, params=''):
    """Fetch objects from IMPACT API with improved rate limiting handling"""
    # max pageSize varies by endpoint, check api docs
    endpoint_url = f"{IMPACT_BASE_URL}/{os.getenv('IMPACT_ACCOUNT_SID')}/{endpoint}?PageSize={pageSize}&{params}"
    logger.info(f"Fetching objects from {endpoint_url} with params {params}")
    headers = {
        "Accept": "application/json",
        f"{os.getenv('IMPACT_ACCOUNT_SID')}": f"{os.getenv('IMPACT_AUTH_TOKEN')}"
    }

    # First, get the total count to determine number of pages
    response = requests.get(f"{endpoint_url}&page=1", headers=headers)
    if response.status_code != 200:
        logger.error(f"Error while fetching the initial page - Response code={response.status_code}")
        return None

    try:
        json_data = response.json()

        total_count = json_data.get("@total", 0)
        page_size = json_data.get("@pagesize", 100)
        total_pages = json_data.get("@numpages", 1)
        current_page = json_data.get("@page", 1)
        logger.info(f"Total results count = {total_count}. lopping through pages of {total_pages}")

        if total_count == 0:
            logger.warning(f"No results returned from API")
            return []

        # Process first page results
        objects_id_and_data = []  # list of tuples (object_id, response_data_json)
        page_results = json_data.get(endpoint, [])
        for result in page_results:
            objects_id_and_data.append((
                str(result.get(oject_id_key, '')),  # object_id
                json.dumps(result)  # response_data as JSON string
            ))

        # If only one page, return results
        if total_pages <= 1:
            return objects_id_and_data

        # For multiple pages, use sequential batches with rate limiting
        def fetch_page_with_retry(page_num, max_retries=3, base_delay=2):
            """Fetch a single page of objects with retry logic"""
            session = create_session()  # Use session with retry logic
            paginated_url = f"{endpoint_url}&Page={page_num}"

            for retry in range(max_retries):
                try:
                    # Add jitter to avoid thundering herd problem
                    if retry > 0:
                        delay = base_delay * (2 ** retry) + random.uniform(0, 1)
                        logger.info(f"Retry {retry} for page {page_num}, waiting {delay:.2f} seconds")
                        time.sleep(delay)

                    response = session.get(paginated_url, headers=headers, timeout=30)

                    # If we hit a rate limit (403 or 429), back off and retry
                    if response.status_code in (403, 429):
                        logger.warning(f"Rate limit hit for page {page_num}, will retry after backoff")
                        continue

                    if response.status_code != 200:
                        logger.error(f"Failed to fetch page {page_num}: Response code: {response.status_code}")
                        return []

                    json_data = response.json()
                    if json_data.get("resultType") != "Success":
                        logger.warning(f"API returned non-success for page {page_num}: {json_data.get('resultType')}")
                        return []

                    page_results = json_data.get("results", [])
                    page_responses = []
                    for result in page_results:
                        page_responses.append((
                            str(result.get(oject_id_key, '')),  # object_id
                            json.dumps(result)  # response_data as JSON string
                        ))
                    logger.info(
                        f"Processed objects at page={page_num}/{total_pages}; added {len(page_responses)} objects")
                    return page_responses

                except Exception as e:
                    logger.error(f"Error processing page {page_num} (attempt {retry + 1}): {str(e)}")
                    if retry == max_retries - 1:
                        return []

            return []  # If we get here, all retries failed

        # Process pages in smaller batches to avoid overwhelming the API
        batch_size = 5  # Process 5 pages at a time
        max_workers = 5  # Use 5 concurrent workers maximum

        remaining_pages = list(range(2, total_pages + 1))  # Start from page 2 as page 1 is already called and processed

        for batch_start in range(0, len(remaining_pages), batch_size):
            batch_pages = remaining_pages[batch_start:batch_start + batch_size]
            logger.info(f"Processing batch of pages {batch_pages[0]}-{batch_pages[-1]}")

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_page = {executor.submit(fetch_page_with_retry, page): page for page in batch_pages}
                for future in concurrent.futures.as_completed(future_to_page):
                    page_responses = future.result()
                    objects_id_and_data.extend(page_responses)

            # Add a delay between batches to avoid rate limiting
            if batch_start + batch_size < len(remaining_pages):
                delay = random.uniform(1.0, 2.0)
                logger.debug(f"Waiting {delay:.2f} seconds before next batch")
                time.sleep(delay)

        logger.info(f"Total objects collected: {len(objects_id_and_data)}")
        return objects_id_and_data

    except Exception as e:
        logger.error(f"Error in fetch_objects_from_api: {str(e)}")
        return None
