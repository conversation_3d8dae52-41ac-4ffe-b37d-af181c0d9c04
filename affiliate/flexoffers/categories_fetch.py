import json
import logging
import os
import time

import requests
from dotenv import load_dotenv

from affiliate.flexoffers.db_ops import bulk_upsert_api_responses, create_categories_table
from common.db_utils import DbUtils
from common.env import ENV_DEV

"""
/categories: Retrieves a list of categories and metadata regarding each category, including the category IDs, 
              then store reponse json in scraped.aff__flexoffers_api_responses  scraped.aff__flexoffers_categories
https://publisherprobeta.flexoffers.com/flexapps/webservices 
example: 
curl -X 'GET' \
  'https://api.flexoffers.com/v3/categories' \
  -H 'accept: application/json' \
  -H 'apiKey: <place_holder>'
"""

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

ENDPOINT = 'categories'

# mapping: flexoffers root category -> cashbackgoat category
# SELECT * FROM cashback_goat.scraped.aff__flexoffers_categories
# where parent_id = 0 order by id limit 100;
'''
flexoffers advertiser categories:
Accessories and Handbags
Apparel
Appliances
Babies and Kids
Beauty and Grooming
Business
Business Solutions
Clothing and Accessories
Computer Hardware
Consumer Electronics
Department Stores
Dietary and Nutritional Supplements
Discounts
Electronics Accessories
Entertainment
Entertainment Tickets
Eyewear
Family
Financial Services
Food and Drink
Footwear
Health and Wellness
Health Care Products & Services
Home and Garden
Home Electronics
Home Goods
Home Improvement and Repair
Hotels and Accommodations
Investment Services
Kitchen and Cooking
Lifestyle and Recreation
Luxury Advertisers
Music / Movies / Games
Office Supplies
Pre-Paid Phone Services
Radio / Television
Shopping
Shopping Networks
Sporting Activities
Sporting & Outdoors Equipment
Sports, Fitness & Outdoors
Sportswear
Subscriptions
Telecommunications
Telephone Services
Travel
Travel Services
Vacations
Vehicle Rentals
Virtual Malls
Wireless

cashback_goat store categories:
Accessories
Auto & Tires
Baby & Toddler
Banking & Finance Tools
Beauty & Wellness
Clothing
Digital Services & Streaming
Electronics
Events & Entertainment
Food, Drinks & Restaurants
Gifts, Flowers & Parties
Home & Garden
Office Supplies
Pets
Shoes
Sports, Outdoors & Fitness
Subscription Boxes & Services
Toys & Games
Travel & Vacations

'''

# mapping: flexoffers root category -> cashbackgoat category
# SELECT * FROM cashback_goat.scraped.aff__flexoffers_categories
# where parent_id = 0 order by id limit 100;
categories_mapping = {
    # Financial
    "Financial Services": "Banking & Finance Tools",
    "Investment Services": "Banking & Finance Tools",

    # Health & Beauty
    "Health and Wellness": "Beauty & Wellness",
    "Beauty and Grooming": "Beauty & Wellness",
    "Dietary and Nutritional Supplements": "Beauty & Wellness",
    "Health Care Products & Services": "Beauty & Wellness",

    # Entertainment
    "Entertainment": "Events & Entertainment",
    "Entertainment Tickets": "Events & Entertainment",
    "Music / Movies / Games": "Events & Entertainment",
    "Radio / Television": "Events & Entertainment",

    # Sports & Outdoors
    "Sports, Fitness & Outdoors": "Sports, Outdoors & Fitness",
    "Sporting Activities": "Sports, Outdoors & Fitness",
    "Sporting & Outdoors Equipment": "Sports, Outdoors & Fitness",
    "Sportswear": "Sports, Outdoors & Fitness",

    # Clothing
    "Clothing and Accessories": "Clothing",
    "Apparel": "Clothing",
    "Footwear": "Shoes",
    "Eyewear": "Accessories",
    "Accessories and Handbags": "Accessories",

    # Electronics
    "Consumer Electronics": "Electronics",
    "Computer Hardware": "Electronics",
    "Electronics Accessories": "Electronics",
    "Home Electronics": "Electronics",
    "Appliances": "Electronics",

    # Travel
    "Travel": "Travel & Vacations",
    "Travel Services": "Travel & Vacations",
    "Vacations": "Travel & Vacations",
    "Hotels and Accommodations": "Travel & Vacations",
    "Vehicle Rentals": "Travel & Vacations",

    # Home
    "Home and Garden": "Home & Garden",
    "Home Goods": "Home & Garden",
    "Home Improvement and Repair": "Home & Garden",
    "Kitchen and Cooking": "Home & Garden",

    # Shopping
    "Shopping": "Accessories",
    "Shopping Networks": "Accessories",
    "Department Stores": "Accessories",
    "Virtual Malls": "Accessories",
    "Luxury Advertisers": "Accessories",
    "Discounts": "Accessories",

    # Digital Services
    "Telecommunications": "Digital Services & Streaming",
    "Telephone Services": "Digital Services & Streaming",
    "Pre-Paid Phone Services": "Digital Services & Streaming",
    "Wireless": "Digital Services & Streaming",
    "Subscriptions": "Subscription Boxes & Services",

    # Family & Gifts
    "Family": "Gifts, Flowers & Parties",
    "Babies and Kids": "Baby & Toddler",

    # Recreation
    "Lifestyle and Recreation": "Toys & Games",

    # Business
    "Business": "Office Supplies",
    "Business Solutions": "Office Supplies",
    "Office Supplies": "Office Supplies",

    # Food
    "Food and Drink": "Food, Drinks & Restaurants"
}


def fetch_categories_from_api():
    """Fetch categories from FlexOffers API and format for database storage"""
    endpoint_url = f"https://api.flexoffers.com/v3/{ENDPOINT}"
    headers = {
        "accept": "application/json",
        "apiKey": f"{os.getenv('FLEXOFFERS_API_KEY')}"
    }
    response = requests.get(endpoint_url, headers=headers)
    logger.info(f"Fetching objects from {endpoint_url}")
    if response.status_code != 200:
        logger.error(f"Error while fetching categories - Response code={response.status_code}")
        return None

    try:
        json_data = response.json()

        # For categories endpoint, the response is directly an array of categories
        # We need to flatten the nested structure for storage
        categories_id_and_data = []

        # Process each top-level category and its subcategories
        for category in json_data:
            # Store the top-level category
            categories_id_and_data.append((int(category.get('id', '')), json.dumps(category)))

            # Process subcategories if they exist
            subcategories = category.get('subCategories', [])
            # there is only one level of subcategories right now
            if subcategories:
                for subcategory in subcategories:
                    categories_id_and_data.append((int(subcategory.get('id', '')), json.dumps(subcategory)))

        logger.info(f"Successfully fetched {len(categories_id_and_data)} categories including subcategories")
        return categories_id_and_data

    except Exception as e:
        logger.error(f"Error processing categories response: {str(e)}")
        return None


def load_raw_json_to_categories_tb(dev_db_utils):
    """Process raw JSON categories data into structured table format
    note: category_names values in json response might be incomplete or have bad chars; use flexoffers_categories.name instead
    """
    logger.info("Processing raw advertiser data into structured table")

    # Ensure the structured table exists
    create_categories_table(dev_db_utils)

    # Query to extract and transform data from the raw responses table
    dev_db_utils.execute_query("""
                               INSERT INTO scraped.aff__flexoffers_categories (id,
                                                                          name,
                                                                          storecategory_name,
                                                                          parent_id,
                                                                          created_at,
                                                                          updated_at)
                               SELECT (response_data ->> 'id')::INTEGER       AS id,
                                      response_data ->> 'name'                AS name,
                                      m.storecategory_name                   as storecategory_name,
                                      (response_data ->> 'parentId')::INTEGER AS parent_id,
                                      res.created_at                          AS created_at,
                                      res.updated_at                          AS updated_at
                               FROM scraped.aff__flexoffers_api_responses res
                                        left join scraped.aff__storecategory_mapping m
                                                  on res.response_data ->> 'name' = m.advertiser_category_name
                                                      and m.network = 'flexoffers'
                               WHERE endpoint = 'categories'
                               ON CONFLICT (id) DO UPDATE SET name                = EXCLUDED.name,
                                                              storecategory_name = EXCLUDED.storecategory_name,
                                                              parent_id           = EXCLUDED.parent_id,
                                                              updated_at          = EXCLUDED.updated_at

                               """)

    # Get count of processed records
    result = dev_db_utils.run_select_query("SELECT COUNT(*) FROM scraped.aff__flexoffers_categories")
    count = result[0][0] if result else 0

    logger.info(f"There is now {count} categories into structured table")

    return count


def main():
    """Main function to fetch and store categories data"""
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)

    start_time = time.time()
    load_dotenv()
    dev_db_utils = DbUtils(env=ENV_DEV)

    # Step 1: Fetch all categories from the API, return list of tuples (id, json)
    categories_id_and_data = fetch_categories_from_api()
    if not categories_id_and_data:
        logger.error("Failed to fetch categories")
        return

    # step 2: Store all advertisers json in the database using bulk operation
    bulk_upsert_api_responses(dev_db_utils=dev_db_utils, objects_id_and_response=categories_id_and_data,
                              endpoint=ENDPOINT)
    logger.info(f"Total categories received from API call: {len(categories_id_and_data)}")

    # step 3: Process raw JSON into structured table
    load_raw_json_to_categories_tb(dev_db_utils)

    # Step 4: Log the results
    result = dev_db_utils.run_select_query("SELECT COUNT(*) FROM scraped.aff__flexoffers_categories")
    count = result[0][0] if result else 0
    logger.info(f"Total categories in database: {count}")

    elapsed_time = time.time() - start_time
    logger.info(f"\nOperation completed in {elapsed_time:.2f} seconds")
    dev_db_utils.close()


if __name__ == "__main__":
    main()
