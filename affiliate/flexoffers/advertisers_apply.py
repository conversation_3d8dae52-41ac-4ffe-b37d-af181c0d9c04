import json  # Add this import at the top if not already present
import logging
import os
import time
from datetime import datetime

import requests
from dotenv import load_dotenv

from affiliate.cj.advertiser_mapping import AdvertiserMapping
from affiliate.flexoffers.db_ops import create_api_responses_table, create_advertisers_table
from common.db_utils import DbUtils
from common.env import ENV_DEV

"""
/advertisers/applyAdvertiser: Apply for any advertiser.
https://publisherprobeta.flexoffers.com/flexapps/webservices 
example:
curl -X 'GET' \
  'https://api.flexoffers.com/v3/advertisers/applyAdvertiser?advertiserId=127&acceptTerms=true' \
  -H 'accept: application/json'
"""

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

def get_unapplied_advertisers(dev_db_utils):
    """Get advertisers that we haven't joined yet"""
    logger.info("Querying for U.S. advertisers we haven't joined yet")
    
    result = dev_db_utils.run_select_query("""
        SELECT advertiser_id, advertiser_name, domain_url 
        FROM scraped.aff__flexoffers_advertisers
        WHERE country='US' AND application_status = 'Apply Now'
        ORDER BY network_rank DESC NULLS LAST, three_month_epc DESC NULLS LAST
        LIMIT 100
    """)
    
    advertisers = []
    for row in result:
        advertisers.append({
            'advertiser_id': row[0],
            'advertiser_name': row[1],
            'domain_url': row[2]
        })
    
    logger.info(f"Found {len(advertisers)} advertisers to apply for")
    return advertisers

def apply_for_advertiser(advertiser_id):
    """Apply for an advertiser program"""
    logger.info(f"Applying for advertiser ID: {advertiser_id}")
    
    headers = {
        "accept": "application/json",
        "apiKey": f"{os.getenv('FLEXOFFERS_API_KEY')}",
        "Content-Type": "application/json"
    }

    url = f'https://api.flexoffers.com/v3/advertisers/applyAdvertiser?advertiserId={advertiser_id}&acceptTerms=true'
    
    # POST request with empty body is sufficient for application
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        logger.info(f"Successfully applied for advertiser ID: {advertiser_id}")
        return True, {"status_code": response.status_code, "response": response.text[:500]}
    else:
        logger.error(f"Failed to apply for advertiser ID: {advertiser_id}")
        logger.error(f"Response code: {response.status_code}; Response content: {response.text[:500]}")
        return False, {"status_code": response.status_code, "response": response.text[:500]}



def apply_for_unapplied_advertisers():
    """Main function to apply for all unapplied advertisers"""
    logger.info("Starting to apply for unapplied advertisers")
    
    load_dotenv()
    dev_db_utils = DbUtils(env=ENV_DEV)
    
    # Get advertisers we haven't joined yet
    advertisers = get_unapplied_advertisers(dev_db_utils)
    
    if not advertisers:
        logger.info("No advertisers to apply for")
        dev_db_utils.close()
        return
    
    # Apply for each advertiser
    success_count = 0
    failure_count = 0
    
    for advertiser in advertisers:
        advertiser_id = advertiser['advertiser_id']
        advertiser_name = advertiser['advertiser_name']
        
        logger.info(f"Processing application for {advertiser_name} (ID: {advertiser_id})")
        
        # Apply for the advertiser
        success, response_data = apply_for_advertiser(advertiser_id)

        if success:
            success_count += 1
        else:
            failure_count += 1
        
        # Add a delay between requests to avoid rate limiting
        time.sleep(1)
    
    logger.info(f"Application process completed. Successes: {success_count}, Failures: {failure_count}")
    dev_db_utils.close()

def main():
    """Main function to apply for advertisers"""
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.INFO)
    start_time = time.time()
    
    apply_for_unapplied_advertisers()
    
    elapsed_time = time.time() - start_time
    logger.info(f"\nOperation completed in {elapsed_time:.2f} seconds")

if __name__ == "__main__":
    main()
