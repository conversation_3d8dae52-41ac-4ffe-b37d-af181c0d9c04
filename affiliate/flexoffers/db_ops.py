import logging

from affiliate.common.advertiser_to_store import AdvertiserToStore

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def create_api_responses_table(dev_db_utils):
    """Create the generalized API response table"""
    dev_db_utils.execute_query("""
                               CREATE TABLE IF NOT EXISTS scraped.aff__flexoffers_api_responses (
                                   id            SERIAL PRIMARY KEY,
                                   object_id     VARCHAR(50) NOT NULL,
                                   endpoint      VARCHAR(50) NOT NULL,
                                   response_data JSONB       NOT NULL,
                                   created_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   UNIQUE (object_id, endpoint)
                               );
                               """)


def create_advertisers_table(dev_db_utils):
    """Create the advertisers table if it doesn't exist"""
    dev_db_utils.execute_query("""
                               CREATE TABLE IF NOT EXISTS scraped.aff__flexoffers_advertisers (
                                   -- Primary identifier
                                   advertiser_id           VARCHAR(50) PRIMARY KEY,

                                   -- Basic information
                                   advertiser_name         VARCHAR(255) NOT NULL,
                                   description             TEXT,
                                   domain_url              TEXT,
                                   image_url               TEXT,

                                   -- Category information
                                   category_ids            INTEGER[],
                                   category_names          TEXT[], -- from /advertiser endpoints, value might be incomplete or have bad chars; use flexoffers_categories.name instead

                                   -- Status information
                                   program_status          VARCHAR(50),
                                   application_status      VARCHAR(50),
                                   application_status_id   INTEGER,
                                   application_status_date TIMESTAMP,
                                   last_status_updated     TIMESTAMP,

                                   -- Performance metrics
                                   seven_day_epc           NUMERIC(10, 2),
                                   thirty_day_epc          NUMERIC(10, 2),
                                   three_month_epc         NUMERIC(10, 2),
                                   network_rank            VARCHAR(10),

                                   -- Commission information
                                   payout                  TEXT,
                                   last_commission_updated TIMESTAMP,

                                   -- Features and capabilities
                                   product_advertiser      BOOLEAN   DEFAULT FALSE,
                                   promotional_advertiser  BOOLEAN   DEFAULT FALSE,
                                   allows_deeplinking      BOOLEAN   DEFAULT FALSE,
                                   deeplink_url            TEXT,
                                   flex_links              BOOLEAN   DEFAULT FALSE,

                                   -- Regional information
                                   country                 VARCHAR(10),

                                   -- Timestamps
                                   created                 TIMESTAMP,

                                   -- Actions (stored as JSONB for flexibility)
                                   actions                 JSONB,

                                   -- Derived fields for easier querying
                                   link_types              TEXT[],

                                   -- Metadata
                                   created_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                               )
                               """)

    # # Create indexes for common query patterns
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_program_status
    #     ON scraped.aff__flexoffers_advertisers(program_status)
    # """)
    #
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_application_status
    #     ON scraped.aff__flexoffers_advertisers(application_status)
    # """)
    #
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_store_name
    #     ON scraped.aff__flexoffers_advertisers(store_name)
    # """)
    #
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_joined
    #     ON scraped.aff__flexoffers_advertisers(joined)
    # """)
    #
    # # Create GIN index for JSONB actions field
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_actions
    #     ON scraped.aff__flexoffers_advertisers USING GIN (actions)
    # """)
    #
    # # Create GIN index for array fields
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_link_types
    #     ON scraped.aff__flexoffers_advertisers USING GIN (link_types)
    # """)


def create_links_table(dev_db_utils):
    """Create the links table if it doesn't exist"""
    dev_db_utils.execute_query("""
                               CREATE TABLE IF NOT EXISTS scraped.aff__flexoffers_links (
                                   -- Link identifiers
                                   link_id             VARCHAR(50) PRIMARY KEY,
                                   -- Advertiser information
                                   advertiser_id       VARCHAR(50) NOT NULL,
                                   advertiser_name     VARCHAR(255),
                                   -- Link content
                                   link_name           TEXT        NOT NULL,
                                   link_description    TEXT,
                                   link_type           VARCHAR(50),
                                   link_url            TEXT        NOT NULL,
                                   html_code           TEXT,
                                   -- Media information
                                   image_url           TEXT,
                                   logo_url            TEXT,
                                   banner_width        INTEGER,
                                   banner_height       INTEGER,
                                   -- Promotion details
                                   promotional_types   TEXT[]    DEFAULT NULL,
                                   coupon_code         VARCHAR(100),
                                   coupon_restrictions TEXT,
                                   dollar_off          NUMERIC(10, 2),
                                   percentage_off      NUMERIC(5, 2),
                                   -- Categories
                                   categories          TEXT,
                                   -- Date information
                                   start_date          DATE,
                                   end_date            DATE,
                                   -- Performance metrics
                                   epc7d               NUMERIC(10, 2),
                                   epc3m               NUMERIC(10, 2),
                                   -- Features
                                   allows_deeplinking  BOOLEAN   DEFAULT FALSE,
                                   -- Metadata
                                   created_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                                   --FOREIGN KEY (advertiser_id) REFERENCES scraped.aff__flexoffers_advertisers(advertiser_id) ON DELETE CASCADE
                               );
                               """)

    # Create indexes for common query patterns
    # dev_db_utils.execute_query("""
    #
    #                            CREATE INDEX IF NOT EXISTS idx_flexoffers_links_advertiser_id
    #                                ON scraped.aff__flexoffers_links(advertiser_id);
    #
    #                            CREATE INDEX IF NOT EXISTS idx_flexoffers_links_link_type
    #                                ON scraped.aff__flexoffers_links(link_type);
    #
    #                            CREATE INDEX IF NOT EXISTS idx_flexoffers_links_promotional_types
    #                                ON scraped.aff__flexoffers_links USING GIN(promotional_types);
    #
    #                            CREATE INDEX IF NOT EXISTS idx_flexoffers_links_coupon_code
    #                                ON scraped.aff__flexoffers_links(coupon_code);
    #
    #                            CREATE INDEX IF NOT EXISTS idx_flexoffers_links_start_end_dates
    #                                ON scraped.aff__flexoffers_links(start_date, end_date);
    #                            """)


def create_advertisers_view(dev_db_utils):
    """Create the advertisers view if it doesn't exist"""
    # TODO: better to refresh deps data first:
    # 1. update scraped.aff__store_mapping:
    #   a. update csv 2. run advertiser_store_mapping.py
    # 2. update scraped.aff__flexoffers_categories
    #   a. update csv 2. run categories_fetch.py
    # AdvertiserToStore().create_affiliate_store_mapping_tb()

    # Create the view using the temporary table
    dev_db_utils.execute_query("""
                               CREATE OR REPLACE VIEW scraped.aff__flexoffers_advertisers_view AS
                               WITH links AS (SELECT DISTINCT ON (advertiser_id) advertiser_id,
                                                                                 link_url
                                              FROM scraped.aff__flexoffers_links
                                              WHERE link_url IS NOT NULL
                                                AND link_url != ''
                                                AND (promotional_types IS NULL OR NOT promotional_types @> ARRAY ['Coupon'])
                                                AND (start_date <= NOW() OR start_date IS NULL)
                                                AND (end_date >= NOW() + INTERVAL '3 day' OR end_date IS NULL)
                                              ORDER BY advertiser_id, end_date DESC),
                                    category_names AS (SELECT a.advertiser_id,
                                                              ARRAY_AGG(DISTINCT c.name)                       AS category_names_clean,
                                                              ARRAY_AGG(DISTINCT c.storecategory_name)
                                                              FILTER (WHERE c.storecategory_name IS NOT NULL) AS storecategory_names
                                                       FROM scraped.aff__flexoffers_advertisers a
                                                                JOIN LATERAL UNNEST(a.category_ids) AS cat_id ON TRUE
                                                                JOIN scraped.aff__flexoffers_categories c ON c.id = cat_id
                                                       GROUP BY a.advertiser_id)
                               SELECT a.*,
                                      sm.store_name                            AS store_name,
                                      COALESCE(a.deeplink_url, links.link_url) AS affiliate_url,
                                      cn.category_names_clean,
                                      cn.storecategory_names
                               FROM scraped.aff__flexoffers_advertisers a
                                        LEFT JOIN scraped.aff__store_mapping sm
                                                  ON a.advertiser_name = sm.advertiser_name
                                        LEFT JOIN links ON a.advertiser_id = links.advertiser_id
                                        LEFT JOIN category_names cn ON a.advertiser_id = cn.advertiser_id
                               WHERE a.application_status = 'Approved'
                                 AND a.program_status = 'Approved'
                                 AND sm.network = 'flexoffers'
                               """)
    logger.info(f"View updated: scraped.aff__flexoffers_advertisers_view")


def bulk_upsert_api_responses(dev_db_utils, objects_id_and_response, endpoint):
    """Store objects of various types and their raw json in the db"""
    # Ensure the table exists
    create_api_responses_table(dev_db_utils)

    # Use ON CONFLICT for PostgreSQL 9.5+ (upsert) with error handling
    upsert_cnt = dev_db_utils.execute_query_in_batch(query=f"""
                                              INSERT INTO scraped.aff__flexoffers_api_responses (
                                                                                            object_id,
                                                                                            endpoint,
                                                                                            response_data,
                                                                                            created_at,
                                                                                            updated_at)
                                              VALUES ( %s, '{endpoint}', %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                                                  ON CONFLICT (object_id, endpoint) DO UPDATE
                                                   SET
                                                       response_data = EXCLUDED.response_data,
                                                   updated_at = CURRENT_TIMESTAMP
                                              """, argslist=objects_id_and_response)

    logger.info(f"Successfully upserted {upsert_cnt} objects in table: scraped.aff__flexoffers_api_responses")


def create_categories_table(dev_db_utils):
    """Create the categories table if it doesn't exist"""
    dev_db_utils.execute_query("""
                               CREATE TABLE IF NOT EXISTS scraped.aff__flexoffers_categories (
                                   id                  INTEGER PRIMARY KEY,
                                   name                VARCHAR(255) NOT NULL,
                                   storecategory_name VARCHAR(255),
                                   parent_id           INTEGER      NOT NULL,
                                   created_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                               );

                               -- CREATE INDEX IF NOT EXISTS idx_flexoffers_categories_parent_id
                               -- ON scraped.aff__flexoffers_categories(parent_id);
                               """)


def create_coupons_table(dev_db_utils):
    """Create the advertisers table if it doesn't exist"""
    dev_db_utils.execute_query("""
                               CREATE TABLE IF NOT EXISTS scraped.aff__flexoffers_coupons (
                                   id TEXT PRIMARY KEY,  -- linkId
                                   
                                   advertiser_id INTEGER,
                                   advertiser_name TEXT,
                                   categories TEXT,

                                   start_date DATE,
                                   end_date DATE,
                                   
                                   link_name TEXT,
                                   link_type TEXT,
                                   link_description TEXT,
                                   link_url TEXT,
                                   
                                   logo_url TEXT,
                                   html_code TEXT,
                                   image_url TEXT,
                                   
                                   coupon_code TEXT,
                                   coupon_restrictions TEXT,

                                   dollar_off NUMERIC,
                                   percentage_off NUMERIC,

                                   promotional_types TEXT,
                                   allows_deeplinking BOOLEAN,
                                   
                                   epc3m NUMERIC,  -- nullable
                                   epc7d NUMERIC,  -- nullable

                                   banner_width INTEGER,
                                   banner_height INTEGER,

                                   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                               )
                               """)

    # # Create indexes for common query patterns
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_program_status
    #     ON scraped.aff__flexoffers_advertisers(program_status)
    # """)
    #
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_application_status
    #     ON scraped.aff__flexoffers_advertisers(application_status)
    # """)
    #
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_store_name
    #     ON scraped.aff__flexoffers_advertisers(store_name)
    # """)
    #
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_joined
    #     ON scraped.aff__flexoffers_advertisers(joined)
    # """)
    #
    # # Create GIN index for JSONB actions field
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_actions
    #     ON scraped.aff__flexoffers_advertisers USING GIN (actions)
    # """)
    #
    # # Create GIN index for array fields
    # dev_db_utils.execute_query("""
    #     CREATE INDEX IF NOT EXISTS idx_flexoffers_advertisers_link_types
    #     ON scraped.aff__flexoffers_advertisers USING GIN (link_types)
    # """)