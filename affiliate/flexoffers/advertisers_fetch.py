import logging
import time

from dotenv import load_dotenv

from affiliate.common.db_util import create_html_decode_function
from affiliate.flexoffers.api_helper import fetch_objects_from_api
from affiliate.flexoffers.db_ops import bulk_upsert_api_responses
from affiliate.flexoffers.db_ops import create_advertisers_table, create_advertisers_view
from common.db_utils import DbUtils
from common.env import ENV_DEV

"""
/advertisers: Retrieve advertisers from flexoffers, then store reponse json in scraped.aff__flexoffers_api_responses
https://publisherprobeta.flexoffers.com/flexapps/webservices 
example: 
curl -X 'GET' \
  'https://api.flexoffers.com/v3/advertisers?ProgramStatus=Approved&ApplicationStatus=All&Page=1&pageSize=25' \
  -H 'accept: application/json' \
  -H 'apiKey: <place_holder>'
"""

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

ENDPOINT = 'advertisers'


def load_raw_json_to_advertisers_tb(dev_db_utils):
    """Process raw JSON advertiser data into structured table format
    note: category_names values in json response might be incomplete or have bad chars; use flexoffers_categories.name instead
    """
    logger.info("Processing raw advertiser data into structured table")

    # Ensure the structured table exists
    create_advertisers_table(dev_db_utils)

    # Create HTML decode function
    create_html_decode_function(dev_db_utils)

    # Query to extract and transform data from the raw responses table
    dev_db_utils.execute_query("""
                               INSERT INTO scraped.aff__flexoffers_advertisers (advertiser_id,
                                                                           advertiser_name,
                                                                           description,
                                                                           domain_url,
                                                                           image_url,
                                                                           category_ids,
                                                                           category_names,
                                                                           program_status,
                                                                           application_status,
                                                                           application_status_id,
                                                                           application_status_date,
                                                                           last_status_updated,
                                                                           seven_day_epc,
                                                                           thirty_day_epc,
                                                                           three_month_epc,
                                                                           network_rank,
                                                                           payout,
                                                                           last_commission_updated,
                                                                           product_advertiser,
                                                                           promotional_advertiser,
                                                                           allows_deeplinking,
                                                                           deeplink_url,
                                                                           flex_links,
                                                                           country,
                                                                           created,
                                                                           actions,
                                                                           link_types,
                                                                           created_at,
                                                                           updated_at)
                               SELECT response_data ->> 'id'                                                         AS advertiser_id,
                                      response_data ->> 'name'                                                       AS advertiser_name,
                                      response_data ->> 'description'                                                AS description,
                                      response_data ->> 'domainUrl'                                                  AS domain_url,
                                      response_data ->> 'imageUrl'                                                   AS image_url,
                                      ARRAY(
                                              SELECT trim(s)::integer
                                              FROM unnest(string_to_array(response_data ->> 'categoryIds', ',')) AS s
                                      )                                                                              AS category_ids,
                                      string_to_array(decode_html_entities(response_data ->> 'categoryNames'),
                                                      ', ')                                                          AS category_names,
                                      response_data ->> 'programStatus'                                              AS program_status,
                                      response_data ->> 'applicationStatus'                                          AS application_status,
                                      (response_data ->> 'applicationStatusId'):: INTEGER                            AS application_status_id,
                                      NULLIF(response_data ->> 'applicationStatusDate', 'null'):: TIMESTAMP          AS application_status_date,
                                      NULLIF(response_data ->> 'lastStatusUpdated', ''):: TIMESTAMP                  AS last_status_updated,
                                      NULLIF(response_data ->> 'sevenDayEpc', ''):: NUMERIC                          AS seven_day_epc,
                                      NULLIF(response_data ->> 'thirtyDayEpc', ''):: NUMERIC                         AS thirty_day_epc,
                                      NULLIF(response_data ->> 'threeMonthEpc', ''):: NUMERIC                        AS three_month_epc,
                                      NULLIF(response_data ->> 'networkRank', '')                                    AS network_rank,
                                      response_data ->> 'payout'                                                     AS payout,
                                      NULLIF(response_data ->> 'lastCommissionUpdated', ''):: TIMESTAMP              AS last_commission_updated,
                                      (response_data ->> 'productAdvertiser')::BOOLEAN                               AS product_advertiser,
                                      (response_data ->> 'promotionalAdvertiser')::BOOLEAN                           AS promotional_advertiser,
                                      (response_data ->> 'allowsDeeplinking')::BOOLEAN                               AS allows_deeplinking,
                                      NULLIF(response_data ->> 'deeplinkURL', 'null')                                AS deeplink_url,
                                      (response_data ->> 'flexLinks')::BOOLEAN                                       AS flex_links,
                                      response_data ->> 'country'                                                    AS country,
                                      NULLIF(response_data ->> 'created', ''):: TIMESTAMP                            AS created,
                                      response_data -> 'actions'                                                     AS actions,
                                      ARRAY(
                                              SELECT DISTINCT jsonb_array_elements(response_data -> 'actions') ->> 'actionType'
                                              WHERE jsonb_typeof(response_data -> 'actions') = 'array'
                                      )                                                                              AS link_types,
                                      created_at                                                              AS created_at,
                                      updated_at                                                              AS updated_at
                               FROM scraped.aff__flexoffers_api_responses
                               WHERE endpoint = 'advertisers'
                               ON CONFLICT (advertiser_id) DO UPDATE SET advertiser_name         = EXCLUDED.advertiser_name,
                                                                         description             = EXCLUDED.description,
                                                                         domain_url              = EXCLUDED.domain_url,
                                                                         image_url               = EXCLUDED.image_url,
                                                                         category_ids            = EXCLUDED.category_ids,
                                                                         category_names          = EXCLUDED.category_names,
                                                                         program_status          = EXCLUDED.program_status,
                                                                         application_status      = EXCLUDED.application_status,
                                                                         application_status_id   = EXCLUDED.application_status_id,
                                                                         application_status_date = EXCLUDED.application_status_date,
                                                                         last_status_updated     = EXCLUDED.last_status_updated,
                                                                         seven_day_epc           = EXCLUDED.seven_day_epc,
                                                                         thirty_day_epc          = EXCLUDED.thirty_day_epc,
                                                                         three_month_epc         = EXCLUDED.three_month_epc,
                                                                         network_rank            = EXCLUDED.network_rank,
                                                                         payout                  = EXCLUDED.payout,
                                                                         last_commission_updated = EXCLUDED.last_commission_updated,
                                                                         product_advertiser      = EXCLUDED.product_advertiser,
                                                                         promotional_advertiser  = EXCLUDED.promotional_advertiser,
                                                                         allows_deeplinking      = EXCLUDED.allows_deeplinking,
                                                                         deeplink_url            = EXCLUDED.deeplink_url,
                                                                         flex_links              = EXCLUDED.flex_links,
                                                                         country                 = EXCLUDED.country,
                                                                         created                 = EXCLUDED.created,
                                                                         actions                 = EXCLUDED.actions,
                                                                         link_types              = EXCLUDED.link_types,
                                                                         updated_at              = EXCLUDED.updated_at
                               """)

    # Get count of processed records
    result = dev_db_utils.run_select_query("SELECT COUNT(*) FROM scraped.aff__flexoffers_advertisers")
    count = result[0][0] if result else 0

    logger.info(f"Successfully processed {count} advertisers into structured table")

    return count


def main():
    """Main function to fetch and store advertisers data"""
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)
    start_time = time.time()
    load_dotenv()
    dev_db_utils = DbUtils(env=ENV_DEV)

    # step 1: Fetch all advertisers regardless of their status
    objects_id_and_data = fetch_objects_from_api(endpoint=ENDPOINT, oject_id_key='id', pageSize=25,
                                                     params='ProgramStatus=Approved&ApplicationStatus=All')

    if not objects_id_and_data:
        logger.error("Failed to fetch advertisers")
        return

    # step 2: Store all advertisers json in the database using bulk operation
    bulk_upsert_api_responses(dev_db_utils=dev_db_utils, objects_id_and_data=objects_id_and_data,
                              endpoint=ENDPOINT)
    logger.info(f"Total advertisers received from API call: {len(objects_id_and_data)}")

    # step 3: Process raw JSON into structured table
    load_raw_json_to_advertisers_tb(dev_db_utils)

    # step 4: (re)Create view for advertisers
    create_advertisers_view(dev_db_utils)

    elapsed_time = time.time() - start_time
    logger.info(f"\nOperation completed in {elapsed_time:.2f} seconds")
    dev_db_utils.close()


if __name__ == "__main__":
    main()
