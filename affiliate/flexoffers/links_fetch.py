import logging
import time

from dotenv import load_dotenv

from affiliate.flexoffers.api_helper import fetch_objects_from_api
from affiliate.flexoffers.db_ops import bulk_upsert_api_responses, create_links_table
from common.db_utils import DbUtils
from common.env import ENV_DEV

"""
/promotions: Retrieve promotional links that have been approved for your domain,
                    then store reponse json in scraped.aff__flexoffers_api_responses
https://publisherprobeta.flexoffers.com/flexapps/webservices 
example: 
curl -X 'GET' \
  'https://api.flexoffers.com/v3/promotions?page=1&pageSize=100' \
  -H 'accept: application/json' \
  -H 'apiKey: <place_holder>'
"""

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

ENDPOINT = 'promotions'


def load_raw_json_to_links_table(dev_db_utils):
    """Process raw JSON link data into structured table format"""
    logger.info("Processing raw link data into structured table")

    # Ensure the structured table exists
    create_links_table(dev_db_utils)

    # Query to extract and transform data from the raw responses table
    dev_db_utils.execute_query("""
                               INSERT INTO scraped.aff__flexoffers_links (link_id,
                                                                     advertiser_id,
                                                                     link_name,
                                                                     link_description,
                                                                     link_type,
                                                                     link_url,
                                                                     html_code,
                                                                     advertiser_name,
                                                                     image_url,
                                                                     logo_url,
                                                                     banner_width,
                                                                     banner_height,
                                                                     promotional_types,
                                                                     coupon_code,
                                                                     coupon_restrictions,
                                                                     dollar_off,
                                                                     percentage_off,
                                                                     categories,
                                                                     start_date,
                                                                     end_date,
                                                                     epc7d,
                                                                     epc3m,
                                                                     allows_deeplinking,
                                                                     created_at,
                                                                     updated_at)
                               SELECT response_data ->> 'linkId'                                        AS link_id,
                                      (response_data ->> 'advertiserId')::VARCHAR                       AS advertiser_id,
                                      response_data ->> 'linkName'                                      AS link_name,
                                      response_data ->> 'linkDescription'                               AS link_description,
                                      response_data ->> 'linkType'                                      AS link_type,
                                      response_data ->> 'linkUrl'                                       AS link_url,
                                      response_data ->> 'htmlCode'                                      AS html_code,
                                      response_data ->> 'advertiserName'                                AS advertiser_name,
                                      response_data ->> 'imageUrl'                                      AS image_url,
                                      response_data ->> 'logoURL'                                       AS logo_url,
                                      NULLIF(response_data ->> 'bannerWidth', '')::INTEGER              AS banner_width,
                                      NULLIF(response_data ->> 'bannerHeight', '')::INTEGER             AS banner_height,
                                      string_to_array(response_data ->> 'promotionalTypes', ',')        AS promotional_types,
                                      response_data ->> 'couponCode'                                    AS coupon_code,
                                      response_data ->> 'couponRestrictions'                            AS coupon_restrictions,
                                      NULLIF(response_data ->> 'dollarOff', '')::NUMERIC                AS dollar_off,
                                      NULLIF(response_data ->> 'percentageOff', '')::NUMERIC            AS percentage_off,
                                      response_data ->> 'categories'                                    AS categories,
                                      NULLIF(response_data ->> 'startDate', '')::DATE                   AS start_date,
                                      NULLIF(response_data ->> 'endDate', '')::DATE                     AS end_date,
                                      NULLIF(response_data ->> 'epc7D', '')::NUMERIC                    AS epc7d,
                                      NULLIF(response_data ->> 'epc3M', '')::NUMERIC                    AS epc3m,
                                      COALESCE((response_data ->> 'allowsDeeplinking')::BOOLEAN, FALSE) AS allows_deeplinking,
                                      created_at                                                 AS created_at,
                                      updated_at                                                 AS updated_at
                               FROM scraped.aff__flexoffers_api_responses
                               WHERE endpoint = 'promotions'
                               ON CONFLICT (link_id) DO UPDATE
                                   SET advertiser_id       = EXCLUDED.advertiser_id,
                                       link_name           = EXCLUDED.link_name,
                                       link_description    = EXCLUDED.link_description,
                                       link_type           = EXCLUDED.link_type,
                                       link_url            = EXCLUDED.link_url,
                                       html_code           = EXCLUDED.html_code,
                                       advertiser_name     = EXCLUDED.advertiser_name,
                                       image_url           = EXCLUDED.image_url,
                                       logo_url            = EXCLUDED.logo_url,
                                       banner_width        = EXCLUDED.banner_width,
                                       banner_height       = EXCLUDED.banner_height,
                                       promotional_types   = EXCLUDED.promotional_types,
                                       coupon_code         = EXCLUDED.coupon_code,
                                       coupon_restrictions = EXCLUDED.coupon_restrictions,
                                       dollar_off          = EXCLUDED.dollar_off,
                                       percentage_off      = EXCLUDED.percentage_off,
                                       categories          = EXCLUDED.categories,
                                       start_date          = EXCLUDED.start_date,
                                       end_date            = EXCLUDED.end_date,
                                       epc7d               = EXCLUDED.epc7d,
                                       epc3m               = EXCLUDED.epc3m,
                                       allows_deeplinking  = EXCLUDED.allows_deeplinking,
                                       updated_at          = CURRENT_TIMESTAMP
                               """)

    # Log the count of processed links
    result = dev_db_utils.run_select_query("SELECT COUNT(*) FROM scraped.aff__flexoffers_links")
    count = result[0][0] if result else 0
    logger.info(f"Total links in structured table: {count}")


def main():
    """Main function to fetch and store advertisers links data"""
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)
    start_time = time.time()
    load_dotenv()
    dev_db_utils = DbUtils(env=ENV_DEV)

    # step 1: Fetch all promotional links that have been approved for my domain.
    objects_id_and_response = fetch_objects_from_api(endpoint=ENDPOINT, oject_id_key='linkId', pageSize=100)

    if not objects_id_and_response:
        logger.error("Failed to fetch links")
        return

    # step 2: Store all links and their json in the database using bulk operation
    bulk_upsert_api_responses(dev_db_utils=dev_db_utils, objects_id_and_response=objects_id_and_response,
                              endpoint=ENDPOINT)
    logger.info(f"Total promotional links received from API call: {len(objects_id_and_response)}")

    # step 3: Process raw JSON into structured table
    load_raw_json_to_links_table(dev_db_utils)

    elapsed_time = time.time() - start_time
    logger.info(f"\nOperation completed in {elapsed_time:.2f} seconds")
    dev_db_utils.close()


if __name__ == "__main__":
    main()
