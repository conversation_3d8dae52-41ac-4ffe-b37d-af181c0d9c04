import logging
import time

from dotenv import load_dotenv

from affiliate.flexoffers.api_helper import fetch_objects_from_api
from affiliate.flexoffers.db_ops import bulk_upsert_api_responses, create_coupons_table
from common.db_utils import DbUtils
from common.env import ENV_DEV

"""
/coupons: Retrieve promotional links that have been approved for your domain,
                    then store reponse json in scraped.aff__flexoffers_api_responses
https://publisherprobeta.flexoffers.com/flexapps/webservices 
example: 
curl -X 'GET' \
  'https://api.flexoffers.com/v3/coupons?page=1&pageSize=100' \
  -H 'accept: application/json'
  -H 'apiKey: <place_holder>'
"""

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

ENDPOINT = 'coupons'

def load_raw_json_to_coupons_tb(dev_db_utils):
    """Process raw JSON coupon data into structured table format"""
    logger.info("Processing raw coupons data into structured table")

    # Ensure the structured table exists
    create_coupons_table(dev_db_utils)

    # Query to extract and transform data from the raw responses table
    dev_db_utils.execute_query("""
                               INSERT INTO scraped.aff__flexoffers_coupons (id,
                                                                       advertiser_id,
                                                                       advertiser_name,
                                                                       categories,
                                                                       start_date,
                                                                       end_date,
                                                                       link_name,
                                                                       link_type,
                                                                       link_description,
                                                                       link_url,
                                                                       logo_url,
                                                                       html_code,
                                                                       image_url,
                                                                       coupon_code,
                                                                       coupon_restrictions,
                                                                       dollar_off,
                                                                       percentage_off,
                                                                       promotional_types,
                                                                       allows_deeplinking,
                                                                       epc3m,
                                                                       epc7d,
                                                                       banner_width,
                                                                       banner_height,
                                                                       created_at,
                                                                       updated_at)
                               SELECT response_data->>'linkId' AS id,
                                      (response_data->>'advertiserId')::INTEGER AS advertiser_id,
                                      response_data->>'advertiserName' AS advertiser_name,
                                      response_data->>'categories' AS categories,
                                      NULLIF(response_data->>'startDate', '')::DATE AS start_date,
                                      NULLIF(response_data->>'endDate', '')::DATE AS end_date,
                                      response_data->>'linkName' AS link_name,
                                      response_data->>'linkType' AS link_type,
                                      response_data->>'linkDescription' AS link_description,
                                      response_data->>'linkUrl' AS link_url,
                                      response_data->>'logoURL' AS logo_url,
                                      response_data->>'htmlCode' AS html_code,
                                      response_data->>'imageUrl' AS image_url,
                                      response_data->>'couponCode' AS coupon_code,
                                      response_data->>'couponRestrictions' AS coupon_restrictions,
                                      NULLIF(response_data->>'dollarOff', '')::NUMERIC AS dollar_off,
                                      NULLIF(response_data->>'percentageOff', '')::NUMERIC AS percentage_off,
                                      response_data->>'promotionalTypes' AS promotional_types,
                                      (response_data->>'allowsDeeplinking')::BOOLEAN AS allows_deeplinking,
                                      NULLIF(response_data->>'epc3M', '')::NUMERIC AS epc3m,
                                      NULLIF(response_data->>'epc7D', '')::NUMERIC AS epc7d,
                                      NULLIF(response_data->>'bannerWidth', '')::INTEGER AS banner_width,
                                      NULLIF(response_data->>'bannerHeight', '')::INTEGER AS banner_height,
                                      CURRENT_TIMESTAMP AS created_at,
                                      CURRENT_TIMESTAMP AS updated_at
                               FROM scraped.aff__flexoffers_api_responses
                               WHERE endpoint = 'coupons'
                               ON CONFLICT (id) DO
                                   UPDATE SET
                                              advertiser_id = EXCLUDED.advertiser_id,
                                              advertiser_name = EXCLUDED.advertiser_name,
                                              categories = EXCLUDED.categories,
                                              start_date = EXCLUDED.start_date,
                                              end_date = EXCLUDED.end_date,
                                              link_name = EXCLUDED.link_name,
                                              link_type = EXCLUDED.link_type,
                                              link_description = EXCLUDED.link_description,
                                              link_url = EXCLUDED.link_url,
                                              logo_url = EXCLUDED.logo_url,
                                              html_code = EXCLUDED.html_code,
                                              image_url = EXCLUDED.image_url,
                                              coupon_code = EXCLUDED.coupon_code,
                                              coupon_restrictions = EXCLUDED.coupon_restrictions,
                                              dollar_off = EXCLUDED.dollar_off,
                                              percentage_off = EXCLUDED.percentage_off,
                                              promotional_types = EXCLUDED.promotional_types,
                                              allows_deeplinking = EXCLUDED.allows_deeplinking,
                                              epc3m = EXCLUDED.epc3m,
                                              epc7d = EXCLUDED.epc7d,
                                              banner_width = EXCLUDED.banner_width,
                                              banner_height = EXCLUDED.banner_height,
                                              updated_at = EXCLUDED.updated_at
                               """)

    # Get count of processed records
    result = dev_db_utils.run_select_query("SELECT COUNT(*) FROM scraped.aff__flexoffers_coupons")
    count = result[0][0] if result else 0

    logger.info(f"Successfully processed {count} {ENDPOINT} into structured table")

    return count


def main():
    """Main function to fetch and store coupon data"""
    logging.basicConfig(format='%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
                        level=logging.DEBUG)
    start_time = time.time()
    load_dotenv()
    dev_db_utils = DbUtils(env=ENV_DEV)

    # step 1: Fetch all promotional links that have been approved for my domain.
    objects_id_and_response = fetch_objects_from_api(endpoint=ENDPOINT, oject_id_key='linkId', pageSize=100)

    if not objects_id_and_response:
        logger.error("Failed to fetch coupons")
        return

    # step 2: Store all links and their json in the database using bulk operation
    bulk_upsert_api_responses(dev_db_utils=dev_db_utils, objects_id_and_response=objects_id_and_response,
                              endpoint=ENDPOINT)
    logger.info(f"Total coupons received from API call: {len(objects_id_and_response)}")

    # step 3: Process raw JSON into structured table
    load_raw_json_to_coupons_tb(dev_db_utils)

    elapsed_time = time.time() - start_time
    logger.info(f"\nOperation completed in {elapsed_time:.2f} seconds")
    dev_db_utils.close()


if __name__ == "__main__":
    main()
